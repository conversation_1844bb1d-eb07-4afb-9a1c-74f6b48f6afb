<!--####################################################-->
<!------------------Filter Start-------------------------->
<!--####################################################-->
<form id="deviceConnectionHistoryFilterform" class="form" [formGroup]="filterDeviceConnectionHistoryForm">

    <!-----------Serial Number start-------------->
    <div class="form-group">
        <label class="form-control-label" for="field_Serial_Number"><strong>{{serialNumberOrHwId}}</strong></label>
        <input class="form-control" type="text" formControlName="serialNumber" />
        <div *ngIf="(filterDeviceConnectionHistoryForm.get('serialNumber').touched || filterDeviceConnectionHistoryForm.get('serialNumber')?.dirty) && 
      filterDeviceConnectionHistoryForm.get('serialNumber').invalid">
            <div *ngIf="filterDeviceConnectionHistoryForm.get('serialNumber').errors['maxlength']" class="pb-2">
                <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterDeviceConnectionHistoryForm.get('serialNumber').errors['pattern']" class="pb-2">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>
    <!-----------Serial Number end-------------->

    <!-----------Device Model start-------------->
    <div class="form-group">
        <label class="form-control-label" for="field_Device_Model"><strong>{{deviceModel}}</strong></label>
        <input class="form-control" type="text" formControlName="deviceModel" />
        <div *ngIf="(filterDeviceConnectionHistoryForm.get('deviceModel').touched || filterDeviceConnectionHistoryForm.get('bridgePartNumber')?.dirty) && 
        filterDeviceConnectionHistoryForm.get('deviceModel').invalid">
            <div *ngIf="filterDeviceConnectionHistoryForm.get('deviceModel').errors['maxlength']" class="pb-2">
                <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterDeviceConnectionHistoryForm.get('deviceModel').errors['pattern']" class="pb-2">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>
    <!-----------Device Model Number end-------------->

    <!-----------Manufacturer start-------------->
    <div class="form-group">
        <label class="form-control-label" for="field_Manufacturer"><strong>{{manufacturer}}</strong></label>
        <input class="form-control" type="text" formControlName="manufacturer" />

        <div *ngIf="(filterDeviceConnectionHistoryForm.get('manufacturer').touched || filterDeviceConnectionHistoryForm.get('manufacturer')?.dirty) && 
          filterDeviceConnectionHistoryForm.get('manufacturer').invalid">
            <div *ngIf="filterDeviceConnectionHistoryForm.get('manufacturer').errors['maxlength']" class="pb-2">
                <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterDeviceConnectionHistoryForm.get('manufacturer').errors['pattern']" class="pb-2">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>
    <!-----------Manufacturer end-------------->

    <!-------------------------------------------->
    <!-- OS Type form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_osType" id="label_osType"><strong>{{osType}}</strong></label>
        <!-- OS Type selection start -->
        <ng-multiselect-dropdown id="field_osType" name="osType" [placeholder]="''" formControlName="osType"
            class="devicePageDeviceType" [settings]="dropdownSettingsForOsType" [data]="osTypeList">
        </ng-multiselect-dropdown>
        <!-- OS Type selection end -->
    </div>
    <!-- OS Type form group end -->
    <!-------------------------------------------->

    <!--------------------------->
    <!-- Last Connected Date - start -->
    <!--------------------------->
    <div class="form-group">
        <label class="form-control-label"
            for="field_LastConnectedEndDate"><strong>{{lastConnectedDate}}</strong></label>
        <mat-form-field>
            <input class="form-control" style="width:90%;display: inherit;" matInput [matDatepicker]="end"
                placeholder="Choose a Modified End Date" formControlName="lastConnectedDateAndTime" [max]="maxdate">
            <mat-datepicker-toggle matSuffix [for]="end"></mat-datepicker-toggle>
            <mat-datepicker #end></mat-datepicker>
        </mat-form-field>
    </div>
    <!------------------------------>
    <!-- Last Connected Date -end -->
    <!------------------------------>
    <!--##################################################-->


    <hr class="mt-1 mb-2">
    <!--####################################################-->
    <!---------Action Button Start------->
    <!--####################################################-->
    <div class="">
        <button class="btn btn-sm btn-orange mr-3" (click)="searchData()"
            [disabled]="filterDeviceConnectionHistoryForm.invalid">{{searchBtnText}}</button>
        <button class="btn btn-sm btn-orange"
            (click)="clearFilter(defaultListingPageReloadSubjectParameter)">{{clearBtnText}}</button>
    </div>
    <!--####################################################-->
    <!---------Action Button End------->
    <!--####################################################-->
</form>
<!--####################################################-->
<!--Filter End-->
<!--####################################################-->