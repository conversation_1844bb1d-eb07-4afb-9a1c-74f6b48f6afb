<!-- filter form start -->
<form id="filter-form" [formGroup]="filterForm" role="form" class="form">
    <!-- itemNumber field start -->
    <div class="form-group">
        <label class="form-control-label" for="field_itemNumber"><strong>{{version}}
            </strong></label>
        <!-- itemNumber input start -->
        <input class="form-control" type="text" formControlName="itemNumber" />
        <!-- itemNumber input end -->
        <!-- validation for itemNumber start -->
        <div *ngIf="(filterForm.get('itemNumber').touched || filterForm.get('itemNumber').dirty) &&
                filterForm.get('itemNumber').invalid ">
            <div *ngIf="filterForm.get('itemNumber').errors['maxlength']">
                <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
            </div>
            <div *ngIf="filterForm.get('itemNumber').errors['pattern']">
                <p class="alert-color">{{specialCharacterErrorMessage}}</p>
            </div>
        </div>
        <!-- validation for itemNumber end -->
    </div>
    <!-- itemNumber field end -->
    <!-- Country filter start -->
    <div class="form-group">
        <!-- Country label start -->
        <label class="form-control-label" for="field_Country"><strong>{{country}}</strong></label>
        <!-- Country label end -->
        <!-- Country multiselect dropdown start -->
        <ng-multiselect-dropdown name="Country" [placeholder]="''" formControlName="country"
            [settings]="dropdownSettingsCountry" [data]="countryList">
        </ng-multiselect-dropdown>
        <!-- Country multiselect dropdown end -->
    </div>
    <!-- Country filter end -->
    <!-- json version filter start -->
    <div class="form-group">
        <!-- json version label start -->
        <label class="form-control-label" for="field_videoJsonVersions"><strong>{{jsonVersion}}</strong></label>
        <!-- json version label end -->
        <!-- json version multiselect dropdown start -->
        <ng-multiselect-dropdown name="jsonVersions" [placeholder]="''" formControlName="jsonVersions"
            [settings]="dropdownSettingsJsonVersions" [data]="jsonVersionList">
        </ng-multiselect-dropdown>
        <!-- json version multiselect dropdown end -->
    </div>
    <!-- json version filter end -->
    <!-- inventory status filter start -->
    <div class="form-group">
        <!-- inventory status label start -->
        <label class="form-control-label" for="field_inventoryStatus"><strong>{{status}}</strong></label>
        <!-- inventory status label end -->
        <!-- inventory multiselect dropdown start -->
        <ng-multiselect-dropdown class="devicePageDeviceType" name="inventoryStatus" [placeholder]="''"
            formControlName="inventoryStatus" [settings]="dropdownSettingsInventoryStatus" [data]="inventoryStatus">
        </ng-multiselect-dropdown>
        <!-- inventory multiselect dropdown end -->
    </div>
    <!-- inventory status filter end -->
    <!-- device type filter start -->
    <div class="form-group">
        <label class="form-control-label" for="field_deviceType"><strong>{{deviceType}}</strong></label>
        <!-- device type multiselect dropdown start -->
        <ng-multiselect-dropdown class="devicePageDeviceType" name="deviceType" [placeholder]="''"
            formControlName="deviceType" [settings]="dropdownSettingsDeviceType" [data]="deviceTypes">
        </ng-multiselect-dropdown>
        <!-- device type multiselect dropdown end -->
    </div>
    <!-- device type filter end -->
    <!-- partNumber field start -->
    <div class="form-group">
        <label class="form-control-label" for="field_partNumber"><strong>{{partNumber}}</strong></label>
        <!-- partNumber input start -->
        <input class="form-control" type="text" formControlName="partNumber" />
        <!-- partNumber input end -->
        <!-- validation for partNumber start -->
        <div
            *ngIf="(filterForm.get('partNumber').touched || filterForm.get('partNumber').dirty) && filterForm.get('partNumber').invalid ">
            <div *ngIf="filterForm.get('partNumber').errors['maxlength']">
                <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
            </div>
            <div *ngIf="filterForm.get('partNumber').errors['pattern']">
                <p class="alert-color">{{specialCharacterErrorMessage}}</p>
            </div>
        </div>
        <!-- validation for partNumber end -->
    </div>
    <!-- partNumber field end -->

    <hr class="mt-1 mb-2">
    <div class="">
        <!-- search filter button start -->
        <button class="btn btn-sm btn-orange mr-3" (click)="searchInventoryFilter()"
            id="inventoryFilterSearch">{{search}}</button>
        <!-- search filter button end -->
        <!-- clear button start -->
        <button class="btn btn-sm btn-orange" (click)="clearFilter()">{{clear}}</button>
        <!--  clear button end -->
    </div>
</form>
<!-- filter form end -->