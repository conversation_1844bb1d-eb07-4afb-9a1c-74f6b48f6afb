import { TestBed } from '@angular/core/testing';

import { DeviceConnectionHistoryOperationService } from './device-connection-history-operation-service.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';

describe('DeviceConnectionHistoryOperationServiceService', () => {
  let service: DeviceConnectionHistoryOperationService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        LocalStorageService,
        SessionStorageService,
        commonsProviders(null)
      ]
    });
    service = TestBed.inject(DeviceConnectionHistoryOperationService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
