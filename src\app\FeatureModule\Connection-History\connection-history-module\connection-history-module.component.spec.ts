import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ConnectionHistoryModuleComponent } from './connection-history-module.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';

describe('ConnectionHistoryModuleComponent', () => {
  let component: ConnectionHistoryModuleComponent;
  let fixture: ComponentFixture<ConnectionHistoryModuleComponent>;

  beforeEach(async () => {
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    await TestBed.configureTestingModule({
      declarations: [ConnectionHistoryModuleComponent],
      providers: [
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        SessionStorageService,
        AuthJwtService,
        commonsProviders(null)]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ConnectionHistoryModuleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
