import { DeviceConnectionHistoryResponse } from "../Model/DeviceConnectionHistoryResponse.model";

/**
* Device Connection History List Result interface for consistent response handling
*/
export interface DeviceConnectionHistoryListResult {
    success: boolean;
    deviceConnectionHistoryList: DeviceConnectionHistoryResponse[];
    totalRecordDisplay: number;
    totalRecord: number;
    localDeviceConnectionHistoryList: DeviceConnectionHistoryResponse[];
    totalItems: number;
    page: number;
}
