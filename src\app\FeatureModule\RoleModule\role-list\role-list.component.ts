import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { ITEMS_PER_PAGE, ListRoleResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { RoleFilterAction } from 'src/app/model/Role/roleFilterAction.model';
import { RolePageResponse } from 'src/app/model/Role/rolePageResponse.model';
import { RoleRequestBody } from 'src/app/model/Role/roleRequestBody.model';
import { RoleResponse } from 'src/app/model/Role/roleResponse.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CreateUpdateRoleService } from 'src/app/shared/modalservice/Role/create-update-role.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { RoleService } from 'src/app/shared/Service/RoleService/role.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-role-list',
  templateUrl: './role-list.component.html',
  styleUrls: ['./role-list.component.css']
})
export class RoleListComponent implements OnInit {

  loading: boolean = false;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 1;
  totalItems: number = 0;

  //Totel Count Display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;
  countItem: number = 0;

  //Page Size DropDown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;

  //Operation List (Update,delete etc);
  operationsList: string[] = [];

  //Filter
  isFilterComponentInitWithApicall: boolean = true;
  listPageRefreshForbackToDetailPage: boolean = false;
  isFilterHidden: boolean = true;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  //Role List
  roleResponseList: RoleResponse[] = [];

  //subscription
  subscriptionForLoading: Subscription;
  subscriptionForRoleListFilterRequestParameter: Subscription;

  //Hide Show List and Detail Page
  roleListDisplay: boolean = true;
  roleDetailDisplay: boolean = false;
  roleId: number = null;

  //unique CheckBox Name
  chkPreFix = "role";
  selectAllCheckboxId = "selectAllRole";
  checkboxListName = "roleItem[]";

  //selected Role Id Collect
  selectedRoleIdList: number[] = [];
  localRoleIdListArray: number[] = [];

  //Permission
  addRolePermission: boolean = false;

  //Role Serach Request
  roleSearchRequestBody: RoleRequestBody = null;

  constructor(private authservice: AuthJwtService,
    private commonsService: CommonsService,
    private permissionService: PermissionService,
    private commonOperationsService: CommonOperationsService,
    private createUpdateRoleService: CreateUpdateRoleService,
    private roleService: RoleService,
    private roleApiCallService: RoleApiCallService,
    private exceptionService: ExceptionHandlingService,
    private commonCheckboxService: CommonCheckboxService,
    private cdr: ChangeDetectorRef) { }

  /**
   * Init methoad
   * <AUTHOR>
   */
  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.operationsList = this.commonOperationsService.accessRoleOperations();
      this.selectedRoleIdList = [];
      this.isFilterComponentInitWithApicall = true;
      this.listPageRefreshForbackToDetailPage = false;
      this.isFilterHidden = false;
      this.roleListDisplay = true;
      this.roleDetailDisplay = false;
      this.setRolePermission();
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.refreshFilter();
    }
    this.subjectInit();
  }


  private subjectInit(): void {
    /**
     * Loading Hide/Display
     * <AUTHOR>
     */
    this.subscriptionForLoading = this.roleService.getRoleListLoadingSubject().subscribe((res: boolean) => {
      this.setLoadingStatus(res);
    });

    /**
     * This Subject call from Filter component
     * Load all the Data
     * <AUTHOR>
     */
    this.subscriptionForRoleListFilterRequestParameter = this.roleService.getRoleListFilterRequestParameterSubject().subscribe((roleRequestParameter: RoleFilterAction) => {
      if (roleRequestParameter.listingPageReloadSubjectParameter.isReloadData) {
        if (roleRequestParameter.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.selectedRoleIdList = [];
          this.setCountItem(this.selectedRoleIdList.length);
          this.resetPage()
        }
        this.loadAll(roleRequestParameter.roleRequestBody);
      }
    });
  }

  /**
   * Destroy subscription
   * <AUTHOR>
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForLoading)) { this.subscriptionForLoading.unsubscribe() }
    if (!isUndefined(this.subscriptionForRoleListFilterRequestParameter)) { this.subscriptionForRoleListFilterRequestParameter.unsubscribe() }
    this.roleService.setRolePermissionList([]);
  }

  private setRolePermission(): void {
    this.addRolePermission = this.permissionService.getRolePermission(PermissionAction.ADD_ROLE_ACTION);
  }

  /**
   * Reset Page
   * <AUTHOR>
   */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
  * Refresh button click
  *
  * <AUTHOR>
  */
  public async clickOnRefreshButton(): Promise<void> {
    this.loading = true;
    this.resetPage();
    // Filter is hidden, directly update the service cache
    const permissionList = await this.roleApiCallService.getRolePermissionList(ListRoleResource);
    this.roleService.setRolePermissionList(permissionList);
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
   * Clear all filter ,Reset Page and Reload the page
   * <AUTHOR>
   */
  public refreshFilter(): void {
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, true);
  }

  /**
  * Item par page Value Changes like (10,50,100)
  * <AUTHOR>
  * @param datasize 
  */
  public changeDataSize(datasize): void {
    this.setLoadingStatus(true);
    this.selectedRoleIdList = [];
    this.setCountItem(this.selectedRoleIdList.length);
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
  * Selected checkbox Role Count
  * <AUTHOR>
  * @param selectedItemSize 
  */
  private setCountItem(selectedItemSize: number): void {
    this.countItem = selectedItemSize;
  }

  /**
  * single Checkbox Select
  * <AUTHOR>
  * @param roleObj 
  * @param isChecked 
  */
  public selectCheckbox(roleObj: RoleResponse, isChecked: boolean): void {
    if (isChecked) {
      this.selectedRoleIdList.push(roleObj.id);
    } else {
      let index = this.selectedRoleIdList.findIndex(obj => obj == roleObj.id);
      this.selectedRoleIdList.splice(index, 1);
    }
    this.setCountItem(this.selectedRoleIdList.length);
    this.defaultSelectAll();
  }

  /**
  * select All checkbox select or deSelect
  * <AUTHOR>
  */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localRoleIdListArray, this.selectedRoleIdList, this.selectAllCheckboxId);
  }

  /**
  * Select All CheckBox
  * <AUTHOR>
  * @param isChecked 
  */
  public selectAllItem(isChecked: boolean): void {
    this.selectedRoleIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localRoleIdListArray, this.selectedRoleIdList, this.checkboxListName);
    this.setCountItem(this.selectedRoleIdList.length);
  }

  /**
  * Change The Page
  * callRoleListRefreshSubject ->Call the filter component
  * filter not clear and send with filter requrest and load data 
  * <AUTHOR>
  * @param page 
  */
  public loadPage(page): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  /**
  * Call Filter component subject and reload page
  * <AUTHOR>
  * @param isDefaultPageNumber 
  * @param isClearFilter 
  */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false)
    this.roleService.callRefreshPageSubject(listingPageReloadSubjectParameter, ListRoleResource, this.isFilterHidden);
  }

  /**
  * Toggle Filter
  * <AUTHOR>
  * @param id 
  */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
  * Update and Delete Role
  * <AUTHOR>
  * @param operationName 
  */
  public changeOperation(operationName: string): void {
    this.commonOperationsService.changeOperationForRole(operationName, ListRoleResource, this.selectedRoleIdList, this.isFilterHidden, this.roleResponseList);
  }



  /**
  * Add Role
  * <AUTHOR>
  */
  public addRole(): void {
    this.createUpdateRoleService.openAddUpdateRolePopup(this.createUpdateRoleService.getCreateRoleParameter(ListRoleResource, this.isFilterHidden)).finally(() => { });
  }

  /**
  * Get Role List
  * <AUTHOR>
  * @param roleRequestBody 
  */
  public loadAll(roleRequestBody: RoleRequestBody): void {
    this.roleSearchRequestBody = roleRequestBody;
    let pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage
    }
    this.setLoadingStatus(true);
    this.roleApiCallService.getRoleList(roleRequestBody, pageObj)?.subscribe({
      next: (rolePageResponse: HttpResponse<RolePageResponse>) => {
        if (rolePageResponse.status == 200) {
          this.paginateDataset(rolePageResponse.body);
        } else {
          this.roleResponseList = [];
          this.totalRecordDisplay = 0;
          this.totalRecord = 0;
          this.loading = false;
        }
        this.setLoadingStatus(false);
      }, error: (error: HttpErrorResponse) => {
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
  * Role Reseponse set 
  * <AUTHOR>
  * @param rolePageResponse 
  */
  private paginateDataset(rolePageResponse: RolePageResponse): void {
    this.totalItems = rolePageResponse.totalElements;
    this.roleResponseList = rolePageResponse.content;
    this.page = rolePageResponse.number + 1;
    this.totalRecord = rolePageResponse.totalElements;
    this.totalRecordDisplay = rolePageResponse.numberOfElements;
    this.setLocalRoleId(this.roleResponseList);
    this.setLoadingStatus(false);
  }

  /**
  * Local Role Id list create for Select all Checkbox
  * <AUTHOR>
  * @param roleIdList 
  */
  public setLocalRoleId(roleIdList: RoleResponse[]): void {
    this.localRoleIdListArray = [];
    for (let roleObj of roleIdList) {
      this.localRoleIdListArray.push(roleObj.id);
    }
    this.defaultSelectAll();
  }

  /**
  * Loading Status 
  * <AUTHOR>
  */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
    this.cdr.detectChanges();
  }


  /**
  * show Role List and Hide detail page
  * <AUTHOR>
  */
  public showRoleList(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = true;
    this.roleId = null;
    this.roleListDisplay = true;
    this.roleDetailDisplay = false;
    this.selectedRoleIdList = [];
    if (this.isFilterHidden) {
      this.filterPageSubjectCallForReloadPage(true, false);
    }
  }

  /**
  * hide Role List and show detail page
  * <AUTHOR>
  * @param id 
  */
  public showRoleDetail(id: number): void {
    this.roleId = id;
    this.roleListDisplay = false;
    this.roleDetailDisplay = true;
  }

}
