import { TestBed } from '@angular/core/testing';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

import { EditSoftwareBuildDialogService } from './edit-software-build-dialog.service';
import { EditSoftwareBuildComponent } from '../../Edit-Software-Build/edit-software-build.component';
import { BasicModelConfig } from 'src/app/model/common/BasicModelConfig.model';
import { SoftwareBuildListResponse } from 'src/app/model/SoftwaarBuilds/SoftwareBuildListResponse.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';

describe('EditInventoryModalService', () => {
  let service: EditSoftwareBuildDialogService;
  let modalServiceSpy: jasmine.SpyObj<NgbModal>;
  let mockModalRef: jasmine.SpyObj<NgbModalRef>;

  beforeEach(() => {
    modalServiceSpy = jasmine.createSpyObj('NgbModal', ['open']);
    mockModalRef = jasmine.createSpyObj('NgbModalRef', ['result'], {
      componentInstance: {
        basicModelConfig: null,
        inventory: null,
        jsonVersionList: null,
        countryList: null
      }
    });

    TestBed.configureTestingModule({
      providers: [
        EditSoftwareBuildDialogService,
        { provide: NgbModal, useValue: modalServiceSpy }
      ]
    });

    service = TestBed.inject(EditSoftwareBuildDialogService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  // ==================== MODAL OPENING TESTS ====================

  describe('Modal Opening', () => {
    let mockBasicModelConfig: BasicModelConfig;
    let mockInventory: SoftwareBuildListResponse;
    let mockJsonVersionList: any[];
    let mockCountryList: CountryListResponse[];

    beforeEach(() => {
      mockBasicModelConfig = new BasicModelConfig('Edit Software Build', 'Edit the software build details', 'Save', 'Cancel');
      mockInventory = {
        id: 1,
        version: 'test-version',
        title: 'Test Software Build',
        partNumber: 'PN123',
        isActive: true,
        deviceTypes: [deviceTypesEnum.CLIENT_DEVICE],
        countries: ['USA'],
        jsonMaster: new Jsonlist(1, 'v1.0')
      } as SoftwareBuildListResponse;
      mockJsonVersionList = [
        new Jsonlist(1, 'v1.0'),
        new Jsonlist(2, 'v2.0')
      ];
      mockCountryList = [
        new CountryListResponse(1, 'USA', ['English']),
        new CountryListResponse(2, 'Canada', ['English', 'French'])
      ];

      modalServiceSpy.open.and.returnValue(mockModalRef);
      mockModalRef.result = Promise.resolve(true);
    });

    it('should open edit inventory modal with default size', async () => {
      const result = service.openEditInventoryModel(
        mockBasicModelConfig,
        mockInventory,
        mockJsonVersionList,
        mockCountryList
      );

      expect(modalServiceSpy.open).toHaveBeenCalledWith(
        EditSoftwareBuildComponent,
        { size: 'sm', modalDialogClass: 'editItemInventoryModelSize' }
      );

      expect(mockModalRef.componentInstance.basicModelConfig).toBe(mockBasicModelConfig);
      expect(mockModalRef.componentInstance.inventory).toBe(mockInventory);
      expect(mockModalRef.componentInstance.jsonVersionList).toBe(mockJsonVersionList);
      expect(mockModalRef.componentInstance.countryList).toBe(mockCountryList);

      const modalResult = await result;
      expect(modalResult).toBe(true);
    });

    it('should open edit inventory modal with large size', async () => {
      const result = service.openEditInventoryModel(
        mockBasicModelConfig,
        mockInventory,
        mockJsonVersionList,
        mockCountryList,
        'lg'
      );

      expect(modalServiceSpy.open).toHaveBeenCalledWith(
        EditSoftwareBuildComponent,
        { size: 'lg', modalDialogClass: 'editItemInventoryModelSize' }
      );

      expect(mockModalRef.componentInstance.basicModelConfig).toBe(mockBasicModelConfig);
      expect(mockModalRef.componentInstance.inventory).toBe(mockInventory);
      expect(mockModalRef.componentInstance.jsonVersionList).toBe(mockJsonVersionList);
      expect(mockModalRef.componentInstance.countryList).toBe(mockCountryList);

      const modalResult = await result;
      expect(modalResult).toBe(true);
    });

    it('should handle modal rejection', async () => {
      mockModalRef.result = Promise.reject(new Error('Modal dismissed'));

      try {
        await service.openEditInventoryModel(
          mockBasicModelConfig,
          mockInventory,
          mockJsonVersionList,
          mockCountryList
        );
        fail('Expected promise to be rejected');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toBe('Modal dismissed');
      }
    });

    it('should handle null parameters', async () => {
      const result = service.openEditInventoryModel(
        null,
        null,
        null,
        null
      );

      expect(modalServiceSpy.open).toHaveBeenCalledWith(
        EditSoftwareBuildComponent,
        { size: 'sm', modalDialogClass: 'editItemInventoryModelSize' }
      );

      expect(mockModalRef.componentInstance.basicModelConfig).toBeNull();
      expect(mockModalRef.componentInstance.inventory).toBeNull();
      expect(mockModalRef.componentInstance.jsonVersionList).toBeNull();
      expect(mockModalRef.componentInstance.countryList).toBeNull();

      const modalResult = await result;
      expect(modalResult).toBe(true);
    });

    it('should handle empty arrays', async () => {
      const result = service.openEditInventoryModel(
        mockBasicModelConfig,
        mockInventory,
        [],
        []
      );

      expect(mockModalRef.componentInstance.jsonVersionList).toEqual([]);
      expect(mockModalRef.componentInstance.countryList).toEqual([]);

      const modalResult = await result;
      expect(modalResult).toBe(true);
    });
  });

  // ==================== SERVICE INJECTION TESTS ====================

  describe('Service Injection', () => {
    it('should inject NgbModal service', () => {
      expect(service['modalService']).toBeDefined();
      expect(service['modalService']).toBe(modalServiceSpy);
    });
  });

  // ==================== RETURN TYPE TESTS ====================

  describe('Return Type', () => {
    it('should return Promise<boolean>', () => {
      const mockBasicModelConfig = new BasicModelConfig('Test', 'Test', 'OK', 'Cancel');
      const mockInventory = {} as SoftwareBuildListResponse;

      modalServiceSpy.open.and.returnValue(mockModalRef);
      mockModalRef.result = Promise.resolve(true);

      const result = service.openEditInventoryModel(
        mockBasicModelConfig,
        mockInventory,
        [],
        []
      );

      expect(result).toBeInstanceOf(Promise);
    });
  });
});
