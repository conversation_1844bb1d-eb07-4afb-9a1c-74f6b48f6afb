import { ProductStatusEnum } from "src/app/shared/enum/Common/ProductStatus.enum";
import { OSTypeEnum } from "src/app/shared/enum/Probe/OSTypeEnum.enum";

export class ProbeListFilterRequestBody {
    serialNumber: string;
    probeTypes: Array<string>;
    features: Array<number>;
    presets: Array<number>;
    customerName: string;
    salesOrderNumbers: Array<string>;
    deviceModel: string;
    manufacturer: string;
    osType: OSTypeEnum;
    featureValidityPeriod: boolean;
    productStatus: Array<ProductStatusEnum>;
    countryIds: Array<number>;
    locked: boolean;
    isEditable: boolean;

    constructor($serialNumber: string, $probeTypes: Array<string>, $features: Array<number>, $presets: Array<number>, $customerName: string, $salesOrderNumbers: Array<string>, // NOSONAR
        $deviceModel: string, $manufacturer: string, $osType: OSTypeEnum, $featureValidityPeriod: boolean, $productStatus: Array<ProductStatusEnum>,
        $countryIds: Array<number>, $locked: boolean, $isEditable: boolean) {
        this.serialNumber = $serialNumber;
        this.probeTypes = $probeTypes;
        this.features = $features;
        this.presets = $presets;
        this.customerName = $customerName;
        this.salesOrderNumbers = $salesOrderNumbers;
        this.deviceModel = $deviceModel;
        this.manufacturer = $manufacturer;
        this.osType = $osType;
        this.featureValidityPeriod = $featureValidityPeriod;
        this.productStatus = $productStatus;
        this.countryIds = $countryIds;
        this.locked = $locked;
        this.isEditable = $isEditable;
    }

}