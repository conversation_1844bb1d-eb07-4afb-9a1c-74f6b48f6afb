<!------------------------------------------->
<!-- loadin start -->
<!------------------------------------------->
<div class="ringLoading" *ngIf="loading">
  <!-- loading gif start -->
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
  <!-- loading gif end -->
</div>
<!------------------------------------------->
<!-- loadin end -->
<!------------------------------------------->

<body class="bg-white" *ngIf="probeDetailDisplay">
  <!------------------------------------------->
  <!-- container fluid start -->
  <!------------------------------------------->
  <div class="container-fluid">
    <!-- row start -->
    <div class="row" id="probeDeatil">
      <div class="col-md-12">
        <!-- probe detail row start -->
        <div class="row headerAlignment">
          <label class="childFlex h5-tag">Probe Detail</label>
          <div class="childFlex">
            <!-- probe operation dropdown start -->
            <!-- selection of probe operation start -->
            <select id="probeOperation" class="form-control form-control-sm mr-3"
              (change)="changeProbeOperation($event)" *ngIf="probeOperations.length > 1">
              <ng-template ngFor let-probeOperation [ngForOf]="probeOperations">
                <option [value]="probeOperation">{{ probeOperation }}</option>
              </ng-template>
            </select>
            <!-- selection of probe operation end -->
            <!--probe operation dropdown end  -->
            <!-- back button start -->
            <button class="btn btn-sm btn-outline-secondary probe-back-btn" (click)="back()"><i class="fa fa-reply"
                aria-hidden="true"></i>&nbsp;&nbsp;{{backBtnText}}</button>
            <!-- back button end -->

            <!-- refresh button start -->
            <button class="btn btn-sm btn-orange ml-2" (click)="refreshProbeDetailPage()" id="otsProbeDetailRefresh"><em
                class="fa fa-refresh"></em></button>
            <!-- refresh button end -->
          </div>
        </div>
        <!-- probe detail row end -->
        <div class="row">
          <!-- ms 12 column start -->
          <div class="col-md-12">
            <!-- card start -->
            <div class="card">
              <div class="card-body">
                <!-- card shadow start -->
                <div class="card shadow">
                  <div class="card-body">

                    <!-- detail fields start -->
                    <div class="row">
                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{salesOrderNumber}}</strong></label>
                          <input type="text" class="form-control" name="salesOrderNumber"
                            [value]="probeDetailWithConfig?.salesOrderNumber" readonly>
                        </div>
                      </div>
                      <!-- serial number div start -->
                      <div class="col-md-3">
                        <!-- serial number form group start -->
                        <div class="form-group">
                          <label><strong class="">{{serialNo}}</strong></label>
                          <input type="text" class="form-control" name="serialNumber"
                            [value]="probeDetailWithConfig?.serialNumber" readonly>
                        </div>
                        <!-- serial number form group end -->
                      </div>
                      <!-- serial number div end -->

                      <!-- probe type div start -->
                      <div class="col-md-3">
                        <!-- probe type form group start -->
                        <div class="form-group">
                          <label><strong class="">{{probeType}}</strong></label>
                          <input type="text" class="form-control" name="probeType" [value]="probeDetailWithConfig?.type"
                            readonly>
                        </div>
                        <!-- probe type form group end -->
                      </div>
                      <!-- probe type div end -->

                      <!-- probe version div start -->
                      <div class="col-md-3">
                        <!-- probe version form group start -->
                        <div class="form-group">
                          <label><strong class="">{{probeVersion}}</strong></label>
                          <input type="text" class="form-control" name="probeVersion"
                            [value]="probeDetailWithConfig?.probeVersion" readonly>
                        </div>
                        <!-- probe version form group end -->
                      </div>
                      <!-- probe version div end -->

                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{partNumber}}</strong></label>
                          <input type="text" class="form-control" name="partNumber"
                            [value]="probeDetailWithConfig?.partNumber" readonly>
                        </div>
                      </div>

                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{modifyDateAndTime}}</strong></label>
                          <input type="text" class="form-control" name="modifiedDate"
                            [value]="probeDetailWithConfig?.modifiedDate | date:dateDisplayFormat" readonly>
                        </div>
                      </div>

                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{lastConnectedDateAndTime}}</strong></label>
                          <input type="text" class="form-control" name="lastConnectedTime"
                            [value]="probeDetailWithConfig?.lastConnectedTime | date:dateDisplayFormat" readonly>
                        </div>
                      </div>

                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{poNumber}}</strong></label>
                          <input type="text" class="form-control" name="poNumber"
                            [value]="probeDetailWithConfig?.poNumber" readonly>
                        </div>
                      </div>

                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{customerName}}</strong></label>
                          <input type="text" class="form-control" name="customerName"
                            [value]="probeDetailWithConfig?.customerName" readonly>
                        </div>
                      </div>

                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{customerEmail}}</strong></label>
                          <input type="text" class="form-control" name="customerEmail"
                            [value]="probeDetailWithConfig?.customerEmail" readonly>
                        </div>
                      </div>

                      <!------------------------------------>
                      <!-- Country field start -->
                      <!------------------------------------>
                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{country}}</strong></label>
                          <input type="text" class="form-control" name="Country"
                            [value]="probeDetailWithConfig?.country" readonly>
                        </div>
                      </div>
                      <!------------------------------------>
                      <!-- Country field End -->
                      <!------------------------------------>

                      <!------------------------------------>
                      <!--- Order Record Type field start -->
                      <!------------------------------------>
                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{orderRecordType}}</strong></label>
                          <input type="text" class="form-control" name="orderRecordType"
                            [value]="probeDetailWithConfig?.orderRecordType" readonly>
                        </div>
                      </div>
                      <!------------------------------------>
                      <!--- Order Record Type field End ---->
                      <!------------------------------------>

                      <!------------------------------------>
                      <!-- Product Status field start -->
                      <!------------------------------------>
                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{status}}</strong></label>
                          <input type="text" class="form-control" name="productStatus"
                            [value]="probeDetailWithConfig?.productStatus | enumMappingDisplayNamePipe:productStatusList"
                            readonly>
                        </div>
                      </div>
                      <!------------------------------------>
                      <!-- Product Status field end ---->
                      <!------------------------------------>

                      <!------------------------------------>
                      <!-- Lock / Unlock field start -->
                      <!------------------------------------>
                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{locked}}</strong></label>
                          <input type="text" class="form-control" name="Locked"
                            [value]="probeDetailWithConfig?.locked | booleanKeyValueMappingDisplayNamePipe:lockUnlockState"
                            required readonly>
                        </div>
                      </div>
                      <!------------------------------------>
                      <!-- Lock / Unlock field end -->
                      <!------------------------------------>

                      <!------------------------------------>
                      <!-- Edit Enable / Disable field start -->
                      <!------------------------------------>
                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">{{editable}}</strong></label>
                          <input type="text" class="form-control" name="Locked"
                            [value]="probeDetailWithConfig?.editable | booleanKeyValueMappingDisplayNamePipe:editEnableDisableState"
                            required readonly>
                        </div>
                      </div>
                      <!------------------------------------>
                      <!-- Edit Enable / Disable field end -->
                      <!------------------------------------>
                    </div>
                    <!-- detail fields end -->

                    <!--form end-->
                  </div>
                </div>
                <!-- card shadow end -->
              </div>
            </div>
            <!-- card end -->
          </div>
          <!-- ms 12 column end -->
        </div>

        <div class="row" *ngIf="probeDetailWithConfig?.presets != null">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="card shadow">
                  <div class="card-body">
                    <div class="container">
                      <label class="mb-1 h5-tag"><span>Preset
                          Configuration</span>
                        <ng-template [ngIf]="probeDetailWithConfig?.reminder != null">
                          <span *ngIf="probeDetailWithConfig?.reminder"> - Reminder Enabled</span>
                          <span *ngIf="!probeDetailWithConfig?.reminder"> - Reminder Disabled</span>
                        </ng-template>
                      </label>
                      <br />
                      <label class="h6-tag mb-0">
                        <span *ngIf="probeDetailWithConfig?.licenseDate!=null">
                          Last Modified Date & Time
                          : {{probeDetailWithConfig?.licenseDate | date:dateDisplayFormat}}</span>
                        <span
                          *ngIf="probeDetailWithConfig?.licenseDate!=null && probeDetailWithConfig?.licenseModifiedBy!=null">
                          | </span>
                        <span *ngIf="probeDetailWithConfig?.licenseModifiedBy!=null">Modified By :
                          {{probeDetailWithConfig?.licenseModifiedBy}}</span>
                      </label>
                      <hr>
                      <!--***************************************************************-->
                      <!------------------------------------------->
                      <!-- table start -->
                      <!------------------------------------------->
                      <!--***************************************************************-->
                      <table class="table table-sm table-bordered" aria-hidden="true">
                        <thead>
                          <tr class="thead-light">
                            <th style="width:20%;">Preset Name</th>
                            <th style="width:15%">Enable</th>
                            <th style="width:25%">Start Date</th>
                            <th style="width:25%">End Date</th>
                            <th style="width:15%" class="trailColumeHide">Trial</th>
                          </tr>
                        </thead>
                        <tbody>
                          <ng-template ngFor let-probeObject [ngForOf]="probeDetailWithConfig?.presets">
                            <tr>
                              <td>{{probeObject | configBaseResponseDisplayPipe}}</td>
                              <td>
                                <span>{{probeObject.enable | commonBooleanValueDisplayPipe}}</span>
                              </td>
                              <td>{{probeObject.startDate | featuresStartEndDateDisplay:true}}</td>
                              <td>{{probeObject.endDate | featuresStartEndDateDisplay:false}}</td>
                              <td class="trailColumeHide">
                                <span>{{probeObject.trial | commonBooleanValueDisplayPipe}}</span>
                              </td>
                            </tr>
                          </ng-template>
                        </tbody>
                      </table>
                      <!--***************************************************************-->
                      <!------------------------------------------->
                      <!-- table end -->
                      <!------------------------------------------->
                      <!--***************************************************************-->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row" *ngIf="probeDetailWithConfig?.features != null">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="card shadow">
                  <div class="card-body">
                    <div class="container">
                      <label class="mb-1 h5-tag"><span>Feature
                          Configuration</span>
                        <ng-template [ngIf]="probeDetailWithConfig?.reminder != null">
                          <span *ngIf="probeDetailWithConfig?.reminder"> - Reminder Enabled</span>
                          <span *ngIf="!probeDetailWithConfig?.reminder"> - Reminder Disabled</span>
                        </ng-template>
                      </label>
                      <br />
                      <label class="h6-tag mb-0">
                        <span *ngIf="probeDetailWithConfig?.licenseDate!=null">
                          Last Modified Date & Time
                          : {{probeDetailWithConfig?.licenseDate | date:dateDisplayFormat}}</span>
                        <span
                          *ngIf="probeDetailWithConfig?.licenseDate!=null && probeDetailWithConfig?.licenseModifiedBy!=null">
                          | </span>
                        <span *ngIf="probeDetailWithConfig?.licenseModifiedBy!=null">Modified By :
                          {{probeDetailWithConfig?.licenseModifiedBy}}</span>
                      </label>
                      <hr>
                      <!--***************************************************************-->
                      <!------------------------------------------->
                      <!-- table start -->
                      <!------------------------------------------->
                      <!--***************************************************************-->
                      <table class="table table-sm table-bordered" aria-hidden="true">
                        <thead>
                          <tr class="thead-light">
                            <th style="width:20%;">Feature Name</th>
                            <th style="width:15%">Enable</th>
                            <th style="width:25%">Start Date</th>
                            <th style="width:25%">End Date</th>
                            <th style="width:15%" class="trailColumeHide">Trial</th>
                          </tr>
                        </thead>
                        <tbody>
                          <ng-template ngFor let-probeObject [ngForOf]="probeDetailWithConfig?.features">
                            <tr>
                              <td>{{probeObject | configBaseResponseDisplayPipe}}</td>
                              <td>
                                <span>{{probeObject.enable | commonBooleanValueDisplayPipe}}</span>
                              </td>
                              <td>{{probeObject.startDate | featuresStartEndDateDisplay:true}}</td>
                              <td>{{probeObject.endDate | featuresStartEndDateDisplay:false}}</td>
                              <td class="trailColumeHide">
                                <span>{{probeObject.trial | commonBooleanValueDisplayPipe}}</span>
                              </td>
                            </tr>
                          </ng-template>
                        </tbody>
                      </table>
                      <!--***************************************************************-->
                      <!------------------------------------------->
                      <!-- table end -->
                      <!------------------------------------------->
                      <!--***************************************************************-->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- ------------------------------------------------------- -->
        <!-- Feature history table - start -->
        <!-- ------------------------------------------------------- -->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="card shadow">
                  <div class="card-body">
                    <div class="container">
                      <div class="d-flex justify-content-between align-items-center">
                        <label class="mb-1 h5-tag"><span>License History</span></label>
                        <button class="btn btn-sm btn-orange" (click)="refreshLicenseHistory()"
                          id="refreshLicenseHistory">
                          <em class="fa fa-refresh"></em>
                        </button>
                      </div>
                      <hr>
                      <div class="row">
                        <div class="col-md-2 text-left mb-3">
                          <label>Show entry</label>
                          <select id="" [(ngModel)]="historyDrpSelectSize" class="form-control form-control-sm"
                            (change)="changeFeatureHistoryDataSize($event)">
                            <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                              <option [value]="dataSize">{{ dataSize }}</option>
                            </ng-template>
                          </select>
                        </div>
                      </div>
                      <!-- ------------------------------------------------------- -->
                      <!-- table start -->
                      <!-- ------------------------------------------------------- -->
                      <table class="table table-sm table-bordered" aria-hidden="true">
                        <!-- table header start -->
                        <thead>
                          <tr class="thead-light">
                            <th>Modified Date & Time</th>
                            <th>Modified By</th>
                            <th>Presets</th>
                            <th>Features</th>
                            <th>Reminder Enabled</th>
                            <th>Action</th>
                          </tr>
                        </thead>
                        <!-- table header end -->
                        <!-- table body start -->
                        <tbody>
                          <ng-template ngFor let-history [ngForOf]="historyList">
                            <tr>
                              <td>{{history.modifiedDate | date:dateDisplayFormat}}</td>
                              <td>{{history.modifiedBy}}</td>
                              <td class="max_col_width"><span *ngIf="history.presets!=null">{{history.presets |
                                  printListPipe}}</span>
                              </td>
                              <td class="max_col_width"><span *ngIf="history.features!=null">{{history.features |
                                  printListPipe}}</span>
                              </td>
                              <td>
                                <span>{{history.reminder | commonBooleanValueDisplayPipe}}</span>
                              </td>
                              <td class="spanunderline" (click)="openProbeConnectionHistory(history)"
                                id="openProbeConnectionHistory">
                                View Details
                              </td>
                            </tr>
                          </ng-template>
                        </tbody>
                        <!-- table body end -->
                      </table>
                      <!-- ------------------------------------------------------- -->
                      <!-- table end -->
                      <!-- ------------------------------------------------------- -->
                      <!--pagination Start-->
                      <div>
                        <div>Showing {{totalHistoryDisplay}} out of {{totalHistories}} Probes</div>
                        <div class="float-right">
                          <!-- ngb pagination start -->
                          <ngb-pagination [collectionSize]="totalHistoryItems" [(page)]="historyPage"
                            [pageSize]="historyItemsPerPage" [maxSize]="5" [rotate]="true" [boundaryLinks]="true"
                            (pageChange)="loadFeatureHistoryPage(historyPage)">
                          </ngb-pagination>
                          <!-- ngb pagination end -->
                        </div>
                      </div>
                      <!--pagination End-->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!------------------------------------------->
        <!--Row End-->
        <!------------------------------------------->
      </div>
    </div>
    <!------------------------------------------->
    <!-- row end -->
    <!------------------------------------------->
  </div>
  <!------------------------------------------->
  <!-- container fluid end -->
  <!------------------------------------------->
</body>


<!------------------------------------------------------------->
<!----------- Tranfer Order Module Start ---------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="transferOrderSelectionDisaplay">
  <app-transfer-order-module (showTranferOrder)="transferOrderSelectionToggle(true,false)"
    [transferProductDetail]="probeDetailForTransferProduct">
  </app-transfer-order-module>
</ng-template>
<!------------------------------------------------------------>
<!--------- Tranfer Order Module End-------------------------->
<!------------------------------------------------------------>