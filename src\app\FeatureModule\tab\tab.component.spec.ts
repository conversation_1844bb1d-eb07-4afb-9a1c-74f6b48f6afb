import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TabComponent } from './tab.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { PermissionService } from 'src/app/shared/permission.service';
import { LocalStorageService } from 'ngx-webstorage';
import { TabActiveEnum } from 'src/app/shared/enum/Tab/tabActiveEnum.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';

describe('TabComponent', () => {
  let component: TabComponent;
  let fixture: ComponentFixture<TabComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let permissionServiceMock: jasmine.SpyObj<PermissionService>;

  beforeEach(async () => {
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    permissionServiceMock = jasmine.createSpyObj('PermissionService', [
      'getDevicePermission',
      'getProbPermission',
      'getJobPermission',
      'getSoftwearBuildPermission',
      'getDeviceLogPermission',
      'getUserPermission',
      'getVideoPermission',
      'getRolePermission',
      'getKitManagementPermission',
      'getSalesOrderPermission',
      'getCountryPermission',
      'getAuditPermission',
      'getProbeConfigGroupPermission',
      'getConnectionHistoryPermission'
    ]);

    await TestBed.configureTestingModule({
      imports: [],
      providers: [
        { provide: PermissionService, useValue: permissionServiceMock },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()]
    })
      .compileComponents();

    fixture = TestBed.createComponent(TabComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.tabActiveEnum).toBe(TabActiveEnum);
      expect(component.noDataDisplay).toBe(false);
      expect(component.deviceRederPermission).toBe(false);
      expect(component.probeReaderPermission).toBe(false);
      expect(component.jobReaderPermission).toBe(false);
      expect(component.softwareBuildReaderPermission).toBe(false);
      expect(component.logReaderPermission).toBe(false);
      expect(component.userReaderPermission).toBe(false);
      expect(component.videoReaderPermission).toBe(false);
      expect(component.roleReaderPermission).toBe(false);
      expect(component.salesOrderReaderPermission).toBe(false);
      expect(component.kitManagementReaderPermission).toBe(false);
      expect(component.countryReaderPermission).toBe(false);
      expect(component.auditReaderPermission).toBe(false);
      expect(component.probeConfigGroupPermission).toBe(false);
    });

    it('should call permission service methods on ngOnInit', () => {
      // Setup permission service return values
      permissionServiceMock.getDevicePermission.and.returnValue(true);
      permissionServiceMock.getProbPermission.and.returnValue(false);
      permissionServiceMock.getJobPermission.and.returnValue(true);
      permissionServiceMock.getSoftwearBuildPermission.and.returnValue(false);
      permissionServiceMock.getDeviceLogPermission.and.returnValue(true);
      permissionServiceMock.getUserPermission.and.returnValue(false);
      permissionServiceMock.getVideoPermission.and.returnValue(true);
      permissionServiceMock.getRolePermission.and.returnValue(false);
      permissionServiceMock.getKitManagementPermission.and.returnValue(true);
      permissionServiceMock.getSalesOrderPermission.and.returnValue(false);
      permissionServiceMock.getCountryPermission.and.returnValue(true);
      permissionServiceMock.getAuditPermission.and.returnValue(false);
      permissionServiceMock.getProbeConfigGroupPermission.and.returnValue(true);

      component.ngOnInit();

      expect(permissionServiceMock.getDevicePermission).toHaveBeenCalledWith(PermissionAction.GET_DEVICE_ACTION);
      expect(permissionServiceMock.getProbPermission).toHaveBeenCalledWith(PermissionAction.GET_PROB_ACTION);
      expect(permissionServiceMock.getJobPermission).toHaveBeenCalledWith(PermissionAction.GET_JOB_ACTION);
      expect(permissionServiceMock.getSoftwearBuildPermission).toHaveBeenCalledWith(PermissionAction.GET_SOFTWARE_BUILD_ACTION);
      expect(permissionServiceMock.getDeviceLogPermission).toHaveBeenCalledWith(PermissionAction.GET_DEVICE_LOG_ACTION);
      expect(permissionServiceMock.getUserPermission).toHaveBeenCalledWith(PermissionAction.GET_USER_ACTION);
      expect(permissionServiceMock.getVideoPermission).toHaveBeenCalledWith(PermissionAction.GET_VIDEO_ACTION);
      expect(permissionServiceMock.getRolePermission).toHaveBeenCalledWith(PermissionAction.GET_ROLE_ACTION);
      expect(permissionServiceMock.getKitManagementPermission).toHaveBeenCalledWith(PermissionAction.KIT_MANAGEMANT_TAB_ACTION);
      expect(permissionServiceMock.getSalesOrderPermission).toHaveBeenCalledWith(PermissionAction.GET_SALES_ORDER_ACTION);
      expect(permissionServiceMock.getCountryPermission).toHaveBeenCalledWith(PermissionAction.GET_COUNTRY_LIST_ACTION);
      expect(permissionServiceMock.getAuditPermission).toHaveBeenCalledWith(PermissionAction.GET_AUDIT_ACTION);
      expect(permissionServiceMock.getProbeConfigGroupPermission).toHaveBeenCalledWith(PermissionAction.GET_CONFIG_GROUP_ACTION);
    });

    it('should set permissions correctly based on service response', () => {
      // Setup permission service return values
      permissionServiceMock.getDevicePermission.and.returnValue(true);
      permissionServiceMock.getProbPermission.and.returnValue(false);
      permissionServiceMock.getJobPermission.and.returnValue(true);
      permissionServiceMock.getSoftwearBuildPermission.and.returnValue(false);
      permissionServiceMock.getDeviceLogPermission.and.returnValue(true);
      permissionServiceMock.getUserPermission.and.returnValue(false);
      permissionServiceMock.getVideoPermission.and.returnValue(true);
      permissionServiceMock.getRolePermission.and.returnValue(false);
      permissionServiceMock.getKitManagementPermission.and.returnValue(true);
      permissionServiceMock.getSalesOrderPermission.and.returnValue(false);
      permissionServiceMock.getCountryPermission.and.returnValue(true);
      permissionServiceMock.getAuditPermission.and.returnValue(false);
      permissionServiceMock.getProbeConfigGroupPermission.and.returnValue(true);

      component.ngOnInit();

      expect(component.deviceRederPermission).toBe(true);
      expect(component.probeReaderPermission).toBe(false);
      expect(component.jobReaderPermission).toBe(true);
      expect(component.softwareBuildReaderPermission).toBe(false);
      expect(component.logReaderPermission).toBe(true);
      expect(component.userReaderPermission).toBe(false);
      expect(component.videoReaderPermission).toBe(true);
      expect(component.roleReaderPermission).toBe(false);
      expect(component.kitManagementReaderPermission).toBe(true);
      expect(component.salesOrderReaderPermission).toBe(false);
      expect(component.countryReaderPermission).toBe(true);
      expect(component.auditReaderPermission).toBe(false);
      expect(component.probeConfigGroupPermission).toBe(true);
    });
  });

  describe('Tab Management', () => {
    beforeEach(() => {
      // Setup default permissions for tab management tests
      permissionServiceMock.getDevicePermission.and.returnValue(true);
      permissionServiceMock.getProbPermission.and.returnValue(true);
      permissionServiceMock.getJobPermission.and.returnValue(true);
      permissionServiceMock.getSoftwearBuildPermission.and.returnValue(false);
      permissionServiceMock.getDeviceLogPermission.and.returnValue(false);
      permissionServiceMock.getUserPermission.and.returnValue(false);
      permissionServiceMock.getVideoPermission.and.returnValue(false);
      permissionServiceMock.getRolePermission.and.returnValue(false);
      permissionServiceMock.getKitManagementPermission.and.returnValue(false);
      permissionServiceMock.getSalesOrderPermission.and.returnValue(false);
      permissionServiceMock.getCountryPermission.and.returnValue(false);
      permissionServiceMock.getAuditPermission.and.returnValue(false);
      permissionServiceMock.getProbeConfigGroupPermission.and.returnValue(false);
    });

    it('should change tab when changeTab is called', () => {
      component.changeTab(TabActiveEnum.probeTab_Active);

      expect(component.tabActiveObject[TabActiveEnum.probeTab_Active]).toBe(true);
      expect(component.tabActiveObject[TabActiveEnum.deviceTab_Active]).toBe(false);
      expect(component.tabActiveObject[TabActiveEnum.jobTab_Active]).toBe(false);
    });

    it('should set only one tab as active when changeTab is called', () => {
      component.changeTab(TabActiveEnum.jobTab_Active);

      const activeTabsCount = Object.values(component.tabActiveObject).filter(value => value === true).length;
      expect(activeTabsCount).toBe(1);
      expect(component.tabActiveObject[TabActiveEnum.jobTab_Active]).toBe(true);
    });

    it('should handle null tab name in changeTab', () => {
      component.changeTab(null);

      const activeTabsCount = Object.values(component.tabActiveObject).filter(value => value === true).length;
      expect(activeTabsCount).toBe(0);
    });
  });

  describe('setActiveTab method', () => {
    it('should set correct tab as active', () => {
      const result = component['setActiveTab'](TabActiveEnum.deviceTab_Active);

      expect(result[TabActiveEnum.deviceTab_Active]).toBe(true);
      expect(result[TabActiveEnum.probeTab_Active]).toBe(false);
      expect(result[TabActiveEnum.jobTab_Active]).toBe(false);
    });

    it('should set all tabs as inactive when null is passed', () => {
      const result = component['setActiveTab'](null);

      const allTabsInactive = Object.values(result).every(value => value === false);
      expect(allTabsInactive).toBe(true);
    });

    it('should return object with all tab enum values', () => {
      const result = component['setActiveTab'](TabActiveEnum.deviceTab_Active);

      const expectedKeys = Object.values(TabActiveEnum);
      const resultKeys = Object.keys(result);

      expectedKeys.forEach(key => {
        expect(resultKeys).toContain(key);
      });
    });
  });

  describe('defaultTabActive method', () => {
    it('should activate first available tab when permissions exist', () => {
      permissionServiceMock.getDevicePermission.and.returnValue(false);
      permissionServiceMock.getProbPermission.and.returnValue(true);
      permissionServiceMock.getJobPermission.and.returnValue(true);

      component.ngOnInit();

      expect(component.tabActiveObject[TabActiveEnum.probeTab_Active]).toBe(true);
      expect(component.noDataDisplay).toBe(false);
    });

    it('should set noDataDisplay to true when no permissions exist', () => {
      // Set all permissions to false
      Object.keys(permissionServiceMock).forEach(method => {
        permissionServiceMock[method].and.returnValue(false);
      });

      component.ngOnInit();

      expect(component.noDataDisplay).toBe(true);
    });

    it('should activate device tab when it has permission and is first in order', () => {
      permissionServiceMock.getDevicePermission.and.returnValue(true);
      permissionServiceMock.getProbPermission.and.returnValue(true);

      component.ngOnInit();

      expect(component.tabActiveObject[TabActiveEnum.deviceTab_Active]).toBe(true);
      expect(component.tabActiveObject[TabActiveEnum.probeTab_Active]).toBe(false);
    });
  });

  describe('prepareTabPermissionMap method', () => {
    beforeEach(() => {
      // Set specific permissions for testing
      component.deviceRederPermission = true;
      component.probeReaderPermission = false;
      component.jobReaderPermission = true;
      component.softwareBuildReaderPermission = false;
      component.logReaderPermission = true;
      component.userReaderPermission = false;
      component.videoReaderPermission = true;
      component.roleReaderPermission = false;
      component.salesOrderReaderPermission = true;
      component.kitManagementReaderPermission = false;
      component.countryReaderPermission = true;
      component.auditReaderPermission = false;
      component.probeConfigGroupPermission = true;
    });

    it('should return map with correct permission mappings', () => {
      const permissionMap = component.prepareTabPermissionMap();

      expect(permissionMap.get(TabActiveEnum.deviceTab_Active)).toBe(true);
      expect(permissionMap.get(TabActiveEnum.probeTab_Active)).toBe(false);
      expect(permissionMap.get(TabActiveEnum.jobTab_Active)).toBe(true);
      expect(permissionMap.get(TabActiveEnum.itemInventoryTab_Active)).toBe(false);
      expect(permissionMap.get(TabActiveEnum.logTab_Active)).toBe(true);
      expect(permissionMap.get(TabActiveEnum.userTab_Active)).toBe(false);
      expect(permissionMap.get(TabActiveEnum.videoTab_Active)).toBe(true);
      expect(permissionMap.get(TabActiveEnum.roleTab_Active)).toBe(false);
      expect(permissionMap.get(TabActiveEnum.salesOrderTab_Active)).toBe(true);
      expect(permissionMap.get(TabActiveEnum.kitManagementTab_Active)).toBe(false);
      expect(permissionMap.get(TabActiveEnum.countryTab_Active)).toBe(true);
      expect(permissionMap.get(TabActiveEnum.auditTab_Active)).toBe(false);
      expect(permissionMap.get(TabActiveEnum.probeConfigGroupTab_Active)).toBe(true);
    });

    it('should return map with all tab enum values as keys', () => {
      const permissionMap = component.prepareTabPermissionMap();

      const expectedKeys = Object.values(TabActiveEnum);
      expectedKeys.forEach(key => {
        expect(permissionMap.has(key)).toBe(true);
      });
    });

    it('should return Map instance', () => {
      const permissionMap = component.prepareTabPermissionMap();

      expect(permissionMap instanceof Map).toBe(true);
    });
  });

  describe('Integration Tests', () => {
    it('should properly initialize and set default tab when component loads', () => {
      permissionServiceMock.getDevicePermission.and.returnValue(false);
      permissionServiceMock.getProbPermission.and.returnValue(false);
      permissionServiceMock.getJobPermission.and.returnValue(true);
      permissionServiceMock.getSoftwearBuildPermission.and.returnValue(false);
      permissionServiceMock.getDeviceLogPermission.and.returnValue(false);
      permissionServiceMock.getUserPermission.and.returnValue(false);
      permissionServiceMock.getVideoPermission.and.returnValue(false);
      permissionServiceMock.getRolePermission.and.returnValue(false);
      permissionServiceMock.getKitManagementPermission.and.returnValue(false);
      permissionServiceMock.getSalesOrderPermission.and.returnValue(false);
      permissionServiceMock.getCountryPermission.and.returnValue(false);
      permissionServiceMock.getAuditPermission.and.returnValue(false);
      permissionServiceMock.getProbeConfigGroupPermission.and.returnValue(false);

      component.ngOnInit();

      expect(component.jobReaderPermission).toBe(true);
      expect(component.tabActiveObject[TabActiveEnum.jobTab_Active]).toBe(true);
      expect(component.noDataDisplay).toBe(false);
    });

    it('should handle scenario where multiple tabs have permissions', () => {
      permissionServiceMock.getDevicePermission.and.returnValue(true);
      permissionServiceMock.getProbPermission.and.returnValue(true);
      permissionServiceMock.getJobPermission.and.returnValue(true);
      permissionServiceMock.getSoftwearBuildPermission.and.returnValue(true);
      permissionServiceMock.getDeviceLogPermission.and.returnValue(true);
      permissionServiceMock.getUserPermission.and.returnValue(true);
      permissionServiceMock.getVideoPermission.and.returnValue(true);
      permissionServiceMock.getRolePermission.and.returnValue(true);
      permissionServiceMock.getKitManagementPermission.and.returnValue(true);
      permissionServiceMock.getSalesOrderPermission.and.returnValue(true);
      permissionServiceMock.getCountryPermission.and.returnValue(true);
      permissionServiceMock.getAuditPermission.and.returnValue(true);
      permissionServiceMock.getProbeConfigGroupPermission.and.returnValue(true);

      component.ngOnInit();

      // Should activate the first tab in the permission map order (device tab)
      expect(component.tabActiveObject[TabActiveEnum.deviceTab_Active]).toBe(true);
      expect(component.noDataDisplay).toBe(false);

      // Only one tab should be active
      const activeTabsCount = Object.values(component.tabActiveObject).filter(value => value === true).length;
      expect(activeTabsCount).toBe(1);
    });

    it('should switch tabs correctly after initialization', () => {
      permissionServiceMock.getDevicePermission.and.returnValue(true);
      permissionServiceMock.getProbPermission.and.returnValue(true);
      permissionServiceMock.getJobPermission.and.returnValue(false);
      permissionServiceMock.getSoftwearBuildPermission.and.returnValue(false);
      permissionServiceMock.getDeviceLogPermission.and.returnValue(false);
      permissionServiceMock.getUserPermission.and.returnValue(false);
      permissionServiceMock.getVideoPermission.and.returnValue(false);
      permissionServiceMock.getRolePermission.and.returnValue(false);
      permissionServiceMock.getKitManagementPermission.and.returnValue(false);
      permissionServiceMock.getSalesOrderPermission.and.returnValue(false);
      permissionServiceMock.getCountryPermission.and.returnValue(false);
      permissionServiceMock.getAuditPermission.and.returnValue(false);
      permissionServiceMock.getProbeConfigGroupPermission.and.returnValue(false);

      component.ngOnInit();

      // Initially device tab should be active
      expect(component.tabActiveObject[TabActiveEnum.deviceTab_Active]).toBe(true);
      expect(component.tabActiveObject[TabActiveEnum.probeTab_Active]).toBe(false);

      // Switch to probe tab
      component.changeTab(TabActiveEnum.probeTab_Active);

      expect(component.tabActiveObject[TabActiveEnum.deviceTab_Active]).toBe(false);
      expect(component.tabActiveObject[TabActiveEnum.probeTab_Active]).toBe(true);
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined tab enum value', () => {
      const undefinedTab = 'nonExistentTab' as TabActiveEnum;
      component.changeTab(undefinedTab);

      // Should not throw error and should set all tabs to false
      const activeTabsCount = Object.values(component.tabActiveObject).filter(value => value === true).length;
      expect(activeTabsCount).toBe(0);
    });

    it('should handle permission service returning undefined', () => {
      permissionServiceMock.getDevicePermission.and.returnValue(undefined);
      permissionServiceMock.getProbPermission.and.returnValue(undefined);
      permissionServiceMock.getJobPermission.and.returnValue(undefined);
      permissionServiceMock.getSoftwearBuildPermission.and.returnValue(undefined);
      permissionServiceMock.getDeviceLogPermission.and.returnValue(undefined);
      permissionServiceMock.getUserPermission.and.returnValue(undefined);
      permissionServiceMock.getVideoPermission.and.returnValue(undefined);
      permissionServiceMock.getRolePermission.and.returnValue(undefined);
      permissionServiceMock.getKitManagementPermission.and.returnValue(undefined);
      permissionServiceMock.getSalesOrderPermission.and.returnValue(undefined);
      permissionServiceMock.getCountryPermission.and.returnValue(undefined);
      permissionServiceMock.getAuditPermission.and.returnValue(undefined);
      permissionServiceMock.getProbeConfigGroupPermission.and.returnValue(undefined);
      permissionServiceMock.getConnectionHistoryPermission.and.returnValue(undefined);

      expect(() => component.ngOnInit()).not.toThrow();
      expect(component.noDataDisplay).toBe(true);
    });

    it('should maintain state consistency when switching between tabs multiple times', () => {
      permissionServiceMock.getDevicePermission.and.returnValue(true);
      permissionServiceMock.getProbPermission.and.returnValue(true);
      permissionServiceMock.getJobPermission.and.returnValue(true);

      component.ngOnInit();

      // Switch between tabs multiple times
      component.changeTab(TabActiveEnum.probeTab_Active);
      component.changeTab(TabActiveEnum.jobTab_Active);
      component.changeTab(TabActiveEnum.deviceTab_Active);
      component.changeTab(TabActiveEnum.probeTab_Active);

      // Verify final state
      expect(component.tabActiveObject[TabActiveEnum.probeTab_Active]).toBe(true);
      expect(component.tabActiveObject[TabActiveEnum.deviceTab_Active]).toBe(false);
      expect(component.tabActiveObject[TabActiveEnum.jobTab_Active]).toBe(false);

      // Verify only one tab is active
      const activeTabsCount = Object.values(component.tabActiveObject).filter(value => value === true).length;
      expect(activeTabsCount).toBe(1);
    });
  });

  describe('Component Properties', () => {
    it('should have correct initial tabActiveObject structure', () => {
      expect(component.tabActiveObject).toBeDefined();
      expect(typeof component.tabActiveObject).toBe('object');
    });

    it('should expose tabActiveEnum publicly', () => {
      expect(component.tabActiveEnum).toBe(TabActiveEnum);
    });

    it('should have all permission properties as boolean type', () => {
      expect(typeof component.deviceRederPermission).toBe('boolean');
      expect(typeof component.probeReaderPermission).toBe('boolean');
      expect(typeof component.jobReaderPermission).toBe('boolean');
      expect(typeof component.softwareBuildReaderPermission).toBe('boolean');
      expect(typeof component.logReaderPermission).toBe('boolean');
      expect(typeof component.userReaderPermission).toBe('boolean');
      expect(typeof component.videoReaderPermission).toBe('boolean');
      expect(typeof component.roleReaderPermission).toBe('boolean');
      expect(typeof component.salesOrderReaderPermission).toBe('boolean');
      expect(typeof component.kitManagementReaderPermission).toBe('boolean');
      expect(typeof component.countryReaderPermission).toBe('boolean');
      expect(typeof component.auditReaderPermission).toBe('boolean');
      expect(typeof component.probeConfigGroupPermission).toBe('boolean');
    });

    it('should have noDataDisplay as boolean type', () => {
      expect(typeof component.noDataDisplay).toBe('boolean');
    });
  });
});
