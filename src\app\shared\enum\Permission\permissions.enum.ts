export enum PermissionsEnum {
    //Device
    DEVICE_ADMIN = "deviceAdmin",
    DEVICE_READER = "deviceReader",
    UPDATE_DEVICE_TYPE = "updateDeviceType",
    LOCK_DEVICE = "lockDevice",
    ASSOCIATE_CUSTOMER_TO_DEVICE = "associateCustomerToDevice",
    DEVICE_DISABLED = "disableDevice",
    DEVICE_RMA = "rmaDevice",
    ENABLE_DISABLE_EDIT_DEVICE = "enableDisableEditDevice",
    TRANSFER_DEVICE = "deviceTransfer",

    //Prob
    PROBE_ADMIN = "probeAdmin",
    PROBE_READER = "probeReader",
    ADD_PROBE = "addProbe",
    PROBE_SET_REMINDER = "probeSetReminder",
    PROBE_DISABLED = "disableProbe",
    ENABLE_DISABLE_EDIT_PROBE = "enableDisableEditProbe",
    PROBE_RMA = "rmaProbe",
    UPDATE_PROBE_TYPE = "updateProbeType",
    ASSIGN_FEATURE_TO_PROBE = "assignFeatureToProbe",
    ASSOCIATE_CUSTOMER_TO_PROBE = "associateCustomerToProbe",
    LOCK_UNLOCK_PROBE = "lockUnlockProbe",
    TANSFER_PROBE = "probeTransfer",

    //Software Build
    SOFTWARE_BUILD_READER = "softwareBuildReader",
    SOFTWARE_BUILD_ADMIN = "softwareBuildAdmin",
    UPDATE_SOFTWARE_BUILD = "updateSoftwareBuild",


    //Role 
    ROLE_ADMIN = "roleAdmin",
    ROLE_READER = "roleReader",
    ADD_ROLE = "addRole",
    UPDATE_ROLE = "updateRole",

    //User
    USER_ADMIN = "userAdmin",
    USER_READER = "userReader",
    ADD_USER = "addUser",
    UPDATE_USER = "updateUser",

    //Video
    VIDEO_ADMIN = "videoAdmin",
    VIDEO_READER = "videoReader",
    ADD_VIDEO = "addVideo",
    UPDATE_VIDEO = "updateVideo",

    //Log
    DEVICE_LOG_READER = "deviceLogReader",

    //Job
    JOB_READER = "jobReader",

    //Country
    COUNTRY_READER = "countryReader",
    COUNTRY_ADMIN = "countryAdmin",
    ADD_COUNTRY = "addCountry",

    //Kit Management
    BRIDGE_KIT_MANAGEMENT_ADMIN = "kitManagementAdmin",
    BRIDGE_KIT_MANAGEMENT_READER = "kitManagementReader",

    //OTSKit Management
    OTS_KIT_MANAGEMENT_ADMIN = "otsKitManagementAdmin",
    OTS_KIT_MANAGEMENT_READER = "otsKitManagementReader",

    //salesOrder
    SALES_ORDER_ADMIN = "salesOrderAdmin",
    SALES_ORDER_READER = "salesOrderReader",
    SALES_ORDER_CONFIGURE_BRIDGE_PROBE = "salesOrderConfigureBridgeAndProbe",
    SALES_ORDER_CONFIGURE_PROBE = "salesOrderConfigureProbe",
    SALES_ORDER_MANUAL_SYNC = "salesOrderManualSync",
    SALES_ORDER_TRANSFER_PRODUCT = "salesOrderTransferProduct",

    //Audit
    AUDIT_READER = "auditReader",
    AUDIT_ADMIN = "auditAdmin",
    AUDIT_DEVICE_REVERSE = "auditReverseActionForDevice",
    AUDIT_PROBE_REVERSE = "auditReverseActionForProbe",

    //Feature Group
    PROBE_CONFIG_GROUP_READER = "probeConfigGroupReader",
    ADD_PROBE_CONFIG_GROUP = "addProbeConfigGroup",
    PROBE_CONFIG_GROUP_ADMIN = "probeConfigGroupAdmin",

    //Connection History
    CONNECTION_HISTORY_READER = "connectionHistoryReader",
    CONNECTION_HISTORY_ADMIN = "connectionHistoryAdmin"
}