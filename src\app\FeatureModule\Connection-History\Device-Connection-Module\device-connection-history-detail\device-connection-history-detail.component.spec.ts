import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeviceConnectionHistoryDetailComponent } from './device-connection-history-detail.component';

describe('DeviceConnectionHistoryDetailComponent', () => {
  let component: DeviceConnectionHistoryDetailComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryDetailComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
