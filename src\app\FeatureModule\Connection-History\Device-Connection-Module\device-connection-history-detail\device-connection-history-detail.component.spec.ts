import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DeviceConnectionHistoryDetailComponent } from './device-connection-history-detail.component';

describe('DeviceConnectionHistoryDetailComponent', () => {
  let component: DeviceConnectionHistoryDetailComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryDetailComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ==================== INPUT PROPERTY TESTS ====================
  describe('Input Properties', () => {
    it('should accept deviceConnectionHistoryId input', () => {
      const testId = 123;

      component.deviceConnectionHistoryId = testId;

      expect(component.deviceConnectionHistoryId).toBe(testId);
    });

    it('should handle null deviceConnectionHistoryId input', () => {
      component.deviceConnectionHistoryId = null;

      expect(component.deviceConnectionHistoryId).toBeNull();
    });

    it('should handle undefined deviceConnectionHistoryId input', () => {
      component.deviceConnectionHistoryId = undefined;

      expect(component.deviceConnectionHistoryId).toBeUndefined();
    });

    it('should handle zero deviceConnectionHistoryId input', () => {
      component.deviceConnectionHistoryId = 0;

      expect(component.deviceConnectionHistoryId).toBe(0);
    });

    it('should handle negative deviceConnectionHistoryId input', () => {
      component.deviceConnectionHistoryId = -1;

      expect(component.deviceConnectionHistoryId).toBe(-1);
    });
  });

  // ==================== OUTPUT PROPERTY TESTS ====================
  describe('Output Properties', () => {
    it('should have showDeviceConnectionHistoryList output emitter', () => {
      expect(component.showDeviceConnectionHistoryList).toBeDefined();
      expect(component.showDeviceConnectionHistoryList.emit).toBeDefined();
    });

    it('should emit showDeviceConnectionHistoryList event', () => {
      spyOn(component.showDeviceConnectionHistoryList, 'emit');

      component.showDeviceConnectionHistoryList.emit();

      expect(component.showDeviceConnectionHistoryList.emit).toHaveBeenCalled();
    });

    it('should emit showDeviceConnectionHistoryList event with data', () => {
      spyOn(component.showDeviceConnectionHistoryList, 'emit');
      const testData = { test: 'data' };

      component.showDeviceConnectionHistoryList.emit(testData);

      expect(component.showDeviceConnectionHistoryList.emit).toHaveBeenCalledWith(testData);
    });
  });

  // ==================== COMPONENT PROPERTIES TESTS ====================
  describe('Component Properties', () => {
    it('should have correct selector', () => {
      expect(component).toBeDefined();
    });

    it('should initialize with default values', () => {
      const newComponent = new DeviceConnectionHistoryDetailComponent();

      expect(newComponent.deviceConnectionHistoryId).toBeUndefined();
      expect(newComponent.showDeviceConnectionHistoryList).toBeDefined();
    });
  });

  // ==================== INTEGRATION TESTS ====================
  describe('Component Integration', () => {
    it('should handle input and output together', () => {
      const testId = 456;
      spyOn(component.showDeviceConnectionHistoryList, 'emit');

      // Set input
      component.deviceConnectionHistoryId = testId;

      // Trigger output
      component.showDeviceConnectionHistoryList.emit();

      expect(component.deviceConnectionHistoryId).toBe(testId);
      expect(component.showDeviceConnectionHistoryList.emit).toHaveBeenCalled();
    });

    it('should maintain input value after output emission', () => {
      const testId = 789;
      component.deviceConnectionHistoryId = testId;

      component.showDeviceConnectionHistoryList.emit();

      expect(component.deviceConnectionHistoryId).toBe(testId);
    });
  });

  // ==================== LIFECYCLE TESTS ====================
  describe('Component Lifecycle', () => {
    it('should handle component initialization', () => {
      expect(component).toBeTruthy();
      expect(component.showDeviceConnectionHistoryList).toBeDefined();
    });

    it('should handle multiple input changes', () => {
      component.deviceConnectionHistoryId = 1;
      expect(component.deviceConnectionHistoryId).toBe(1);

      component.deviceConnectionHistoryId = 2;
      expect(component.deviceConnectionHistoryId).toBe(2);

      component.deviceConnectionHistoryId = 3;
      expect(component.deviceConnectionHistoryId).toBe(3);
    });

    it('should handle component destruction gracefully', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });
  });

  // ==================== TYPE SAFETY TESTS ====================
  describe('Type Safety', () => {
    it('should accept number type for deviceConnectionHistoryId', () => {
      const numberId: number = 123;

      component.deviceConnectionHistoryId = numberId;

      expect(typeof component.deviceConnectionHistoryId).toBe('number');
      expect(component.deviceConnectionHistoryId).toBe(123);
    });

    it('should handle large number values', () => {
      const largeId = Number.MAX_SAFE_INTEGER;

      component.deviceConnectionHistoryId = largeId;

      expect(component.deviceConnectionHistoryId).toBe(largeId);
    });

    it('should handle small number values', () => {
      const smallId = Number.MIN_SAFE_INTEGER;

      component.deviceConnectionHistoryId = smallId;

      expect(component.deviceConnectionHistoryId).toBe(smallId);
    });
  });

  // ==================== ERROR HANDLING TESTS ====================
  describe('Error Handling', () => {
    it('should handle invalid input gracefully', () => {
      expect(() => {
        component.deviceConnectionHistoryId = NaN;
      }).not.toThrow();

      expect(component.deviceConnectionHistoryId).toBeNaN();
    });

    it('should handle Infinity input', () => {
      component.deviceConnectionHistoryId = Infinity;

      expect(component.deviceConnectionHistoryId).toBe(Infinity);
    });

    it('should handle -Infinity input', () => {
      component.deviceConnectionHistoryId = -Infinity;

      expect(component.deviceConnectionHistoryId).toBe(-Infinity);
    });
  });
});
