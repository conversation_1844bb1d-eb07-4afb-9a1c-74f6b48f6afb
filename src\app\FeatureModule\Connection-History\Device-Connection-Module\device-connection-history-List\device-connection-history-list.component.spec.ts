import { ComponentFixture, TestBed } from '@angular/core/testing';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { DeviceConnectionHistoryListComponent } from './device-connection-history-list.component';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('DeviceConnectionHistoryModuleComponent', () => {
  let component: DeviceConnectionHistoryListComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryListComponent>;

  beforeEach(async () => {
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryListComponent],
      providers: [
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        PrintListPipe,
        commonsProviders(null)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
