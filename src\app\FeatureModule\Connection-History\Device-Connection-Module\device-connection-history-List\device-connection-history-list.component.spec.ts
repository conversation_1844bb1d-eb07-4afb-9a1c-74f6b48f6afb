import { ComponentFixture, TestBed } from '@angular/core/testing';
import { commonsProviders, testAuthentication, testToggleFilter } from 'src/app/Tesing-Helper/test-utils';
import { DeviceConnectionHistoryListComponent } from './device-connection-history-list.component';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { DeviceConnectionHistoryOperationService } from '../Device-Connection-History-Service/Device-Connection-History-Operation/device-connection-history-operation-service.service';
import { ToastrService } from 'ngx-toastr';
import { Subject, of } from 'rxjs';
import { DeviceConnectionHistoryFilterAction } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryFilterAction.model';
import { DeviceConnectionHistoryResponse } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryResponse.model';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistorySearchRequestBody.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ITEMS_PER_PAGE } from 'src/app/app.constants';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { OSTypeEnum } from 'src/app/shared/enum/Probe/OSTypeEnum.enum';
import { ConnectionTypeEnum } from 'src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum';

describe('DeviceConnectionHistoryListComponent', () => {
  let component: DeviceConnectionHistoryListComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryListComponent>;

  // Service mocks
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let commonCheckboxServiceSpy: jasmine.SpyObj<CommonCheckboxService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let deviceConnectionHistoryOperationServiceSpy: jasmine.SpyObj<DeviceConnectionHistoryOperationService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;

  // Mock data
  // Import ConnectionTypeEnum at the top if not already imported:
  // import { ConnectionTypeEnum } from 'src/app/shared/enum/ConnectionTypeEnum.enum';
  const mockDeviceConnectionHistoryResponse: DeviceConnectionHistoryResponse = {
    id: 1,
    deviceSerialNumber: 'TEST123',
    deviceModel: 'TestModel',
    manufacturer: 'TestManufacturer',
    osType: OSTypeEnum.BRIDGE,
    connectionType: ConnectionTypeEnum.INTERNAL,
    lastConnectedDate: 1672531200000
  };

  const mockDeviceConnectionHistoryList: DeviceConnectionHistoryResponse[] = [
    mockDeviceConnectionHistoryResponse,
    { ...mockDeviceConnectionHistoryResponse, id: 2, deviceSerialNumber: 'TEST456' }
  ];

  const mockSearchRequestBody = new DeviceConnectionHistorySearchRequestBody(
    'TestManufacturer',
    'TestModel',
    'TEST123',
    OSTypeEnum.BRIDGE,
    1672531200000
  );

  beforeEach(async () => {
    // Create service spies
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['accessDataSizes']);
    commonCheckboxServiceSpy = jasmine.createSpyObj('CommonCheckboxService', [
      'defaultSelectAll', 'selectAllItem', 'clearSelectAllCheckbox'
    ]);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getConnectionHistoryPermission']);
    deviceConnectionHistoryOperationServiceSpy = jasmine.createSpyObj('DeviceConnectionHistoryOperationService', [
      'getDeviceConnectionHistoryListFilterRequestParameterSubject',
      'callRefreshPageSubject',
      'loadDeviceConnectionHistoryList'
    ]);
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Set up default return values
    authServiceSpy.isAuthenticate.and.returnValue(true);
    commonsServiceSpy.accessDataSizes.and.returnValue(['10', '25', '50', '100']);
    permissionServiceSpy.getConnectionHistoryPermission.and.returnValue(true);

    // Create mock subjects
    const mockFilterSubject = new Subject<DeviceConnectionHistoryFilterAction>();
    deviceConnectionHistoryOperationServiceSpy.getDeviceConnectionHistoryListFilterRequestParameterSubject.and.returnValue(mockFilterSubject);

    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryListComponent],
      providers: [
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: CommonCheckboxService, useValue: commonCheckboxServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: DeviceConnectionHistoryOperationService, useValue: deviceConnectionHistoryOperationServiceSpy },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        PrintListPipe,
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryListComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ==================== AUTHENTICATION TESTS ====================
  describe('Authentication', () => {
    it('should redirect to login when user is not authenticated', () => {
      testAuthentication(authServiceSpy, component, fixture);
    });

    it('should initialize component when user is authenticated', () => {
      authServiceSpy.isAuthenticate.and.returnValue(true);

      component.ngOnInit();
      fixture.detectChanges();

      expect(component.page).toBe(0);
      expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
      expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
      expect(component.previousPage).toBe(1);
      expect(component.selectedDeviceConnectionHistoryIdList).toEqual([]);
      expect(component.isFilterComponentInitWithApicall).toBe(true);
      expect(component.listPageRefreshForbackToDetailPage).toBe(false);
      expect(component.isFilterHidden).toBe(false);
      expect(component.deviceConnectionHistoryListDisplay).toBe(true);
      expect(component.deviceConnectionHistoryDetailDisplay).toBe(false);
    });
  });

  // ==================== INITIALIZATION TESTS ====================
  describe('Component Initialization', () => {
    beforeEach(() => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
    });

    it('should set connection history permission on init', () => {
      permissionServiceSpy.getConnectionHistoryPermission.and.returnValue(true);

      component.ngOnInit();

      expect(permissionServiceSpy.getConnectionHistoryPermission).toHaveBeenCalledWith(PermissionAction.GET_CONNECTION_HISTORY_ACTION);
      expect(component.connectioHistoryAdminPermission).toBe(true);
    });

    it('should initialize data sizes from commons service', () => {
      const mockDataSizes = ['10', '25', '50', '100'];
      commonsServiceSpy.accessDataSizes.and.returnValue(mockDataSizes);

      component.ngOnInit();

      expect(commonsServiceSpy.accessDataSizes).toHaveBeenCalled();
      expect(component.dataSizes).toEqual(mockDataSizes);
    });

    it('should initialize subject subscriptions', () => {
      spyOn(component as any, 'subjectInit');

      component.ngOnInit();

      expect((component as any).subjectInit).toHaveBeenCalled();
    });
  });

  // ==================== SUBSCRIPTION TESTS ====================
  describe('Subject Subscriptions', () => {
    beforeEach(() => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
    });

    it('should handle filter request parameter subject subscription', () => {
      const mockFilterAction = new DeviceConnectionHistoryFilterAction(
        new ListingPageReloadSubjectParameter(true, true, false, false),
        mockSearchRequestBody
      );

      spyOn(component, 'loadAll');

      component.ngOnInit();

      // Emit filter action
      const filterSubject = deviceConnectionHistoryOperationServiceSpy.getDeviceConnectionHistoryListFilterRequestParameterSubject();
      filterSubject.next(mockFilterAction);

      expect(component.loadAll).toHaveBeenCalledWith(mockSearchRequestBody);
      expect(component.selectedDeviceConnectionHistoryIdList).toEqual([]);
      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(1);
    });

    it('should not reload data when isReloadData is false', () => {
      const mockFilterAction = new DeviceConnectionHistoryFilterAction(
        new ListingPageReloadSubjectParameter(false, true, false, false),
        mockSearchRequestBody
      );

      spyOn(component, 'loadAll');

      component.ngOnInit();

      // Emit filter action with isReloadData false
      const filterSubject = deviceConnectionHistoryOperationServiceSpy.getDeviceConnectionHistoryListFilterRequestParameterSubject();
      filterSubject.next(mockFilterAction);

      expect(component.loadAll).not.toHaveBeenCalled();
    });

    it('should not reset page when isDefaultPageNumber is false', () => {
      const mockFilterAction = new DeviceConnectionHistoryFilterAction(
        new ListingPageReloadSubjectParameter(true, false, false, false),
        mockSearchRequestBody
      );

      spyOn(component, 'loadAll');
      spyOn(component as any, 'resetPage');

      component.ngOnInit();

      // Emit filter action with isDefaultPageNumber false
      const filterSubject = deviceConnectionHistoryOperationServiceSpy.getDeviceConnectionHistoryListFilterRequestParameterSubject();
      filterSubject.next(mockFilterAction);

      expect((component as any).resetPage).not.toHaveBeenCalled();
      expect(component.loadAll).toHaveBeenCalledWith(mockSearchRequestBody);
    });
  });

  // ==================== LIFECYCLE TESTS ====================
  describe('Component Lifecycle', () => {
    it('should unsubscribe from filter subscription on destroy', () => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      component.ngOnInit();

      // Create a spy on the subscription
      const subscriptionSpy = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component['subscriptionForDeviceConnectionHistoryListFilterRequestParameter'] = subscriptionSpy;

      component.ngOnDestroy();

      expect(subscriptionSpy.unsubscribe).toHaveBeenCalled();
    });

    it('should handle undefined subscription on destroy', () => {
      component['subscriptionForDeviceConnectionHistoryListFilterRequestParameter'] = undefined;

      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  // ==================== PAGINATION TESTS ====================
  describe('Pagination', () => {
    beforeEach(() => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      component.ngOnInit();
    });

    it('should reset page correctly', () => {
      component.page = 5;
      component.previousPage = 3;

      (component as any).resetPage();

      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(1);
    });

    it('should change data size and reload page', () => {
      spyOn(component, 'filterPageSubjectCallForReloadPage');
      spyOn(component as any, 'setLoadingStatus');

      const mockEvent = { target: { value: '50' } };

      component.changeDataSize(mockEvent);

      expect((component as any).setLoadingStatus).toHaveBeenCalledWith(true);
      expect(component.selectedDeviceConnectionHistoryIdList).toEqual([]);
      expect(component.itemsPerPage).toBe(50);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
    });

    it('should load page when page number changes', () => {
      spyOn(component, 'filterPageSubjectCallForReloadPage');
      component.previousPage = 1;

      component.loadPage(2);

      expect(component.previousPage).toBe(2);
      expect(commonCheckboxServiceSpy.clearSelectAllCheckbox).toHaveBeenCalledWith(component.selectAllCheckboxId);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(false, false);
    });

    it('should not load page when page number is same', () => {
      spyOn(component, 'filterPageSubjectCallForReloadPage');
      component.previousPage = 2;

      component.loadPage(2);

      expect(component.filterPageSubjectCallForReloadPage).not.toHaveBeenCalled();
    });
  });

  // ==================== CHECKBOX TESTS ====================
  describe('Checkbox Operations', () => {
    beforeEach(() => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      component.ngOnInit();
    });

    it('should select checkbox and add to selected list', () => {
      spyOn(component as any, 'defaultSelectAll');

      component.selectCheckbox(mockDeviceConnectionHistoryResponse, true);

      expect(component.selectedDeviceConnectionHistoryIdList).toContain(1);
      expect((component as any).defaultSelectAll).toHaveBeenCalled();
    });

    it('should deselect checkbox and remove from selected list', () => {
      component.selectedDeviceConnectionHistoryIdList = [1, 2];
      spyOn(component as any, 'defaultSelectAll');

      component.selectCheckbox(mockDeviceConnectionHistoryResponse, false);

      expect(component.selectedDeviceConnectionHistoryIdList).not.toContain(1);
      expect(component.selectedDeviceConnectionHistoryIdList).toContain(2);
      expect((component as any).defaultSelectAll).toHaveBeenCalled();
    });

    it('should call defaultSelectAll method', () => {
      component.localDeviceConnectionHistoryIdListArray = [1, 2];
      component.selectedDeviceConnectionHistoryIdList = [1];

      (component as any).defaultSelectAll();

      expect(commonCheckboxServiceSpy.defaultSelectAll).toHaveBeenCalledWith(
        component.localDeviceConnectionHistoryIdListArray,
        component.selectedDeviceConnectionHistoryIdList,
        component.selectAllCheckboxId
      );
    });

    it('should select all items', () => {
      const mockSelectedList = [1, 2];
      commonCheckboxServiceSpy.selectAllItem.and.returnValue(mockSelectedList);

      component.selectAllItem(true);

      expect(commonCheckboxServiceSpy.selectAllItem).toHaveBeenCalledWith(
        true,
        component.localDeviceConnectionHistoryIdListArray,
        component.selectedDeviceConnectionHistoryIdList,
        component.checkboxListName
      );
      expect(component.selectedDeviceConnectionHistoryIdList).toEqual(mockSelectedList);
    });
  });

  // ==================== FILTER TESTS ====================
  describe('Filter Operations', () => {
    beforeEach(() => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      component.ngOnInit();
    });

    it('should toggle filter visibility', () => {
      testToggleFilter(component);
    });

    it('should refresh filter and clear filters', async () => {
      spyOn(component as any, 'resetPage');
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.loading = false;

      await component.refreshFilter(true);

      expect(component.loading).toBe(true);
      expect((component as any).resetPage).toHaveBeenCalled();
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, true);
    });

    it('should refresh filter without clearing filters', async () => {
      spyOn(component as any, 'resetPage');
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.loading = false;

      await component.refreshFilter(false);

      expect(component.loading).toBe(true);
      expect((component as any).resetPage).toHaveBeenCalled();
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
    });

    it('should call filter page subject for reload page', () => {
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.filterPageSubjectCallForReloadPage(true, false);

      expect(deviceConnectionHistoryOperationServiceSpy.callRefreshPageSubject).toHaveBeenCalledWith(
        jasmine.any(ListingPageReloadSubjectParameter),
        jasmine.any(String),
        component.isFilterHidden
      );
    });

    it('should click on refresh button', async () => {
      spyOn(component as any, 'resetPage');
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.loading = false;

      await component.clickOnRefreshButton();

      expect(component.loading).toBe(true);
      expect((component as any).resetPage).toHaveBeenCalled();
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
    });
  });

  // ==================== DATA LOADING TESTS ====================
  describe('Data Loading', () => {
    beforeEach(() => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      component.ngOnInit();
    });

    it('should load all data successfully', async () => {
      const mockResult = {
        success: true,
        deviceConnectionHistoryList: mockDeviceConnectionHistoryList,
        totalRecordDisplay: 2,
        totalRecord: 10,
        totalItems: 10,
        page: 1,
        localDeviceConnectionHistoryList: mockDeviceConnectionHistoryList
      };

      deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryList.and.returnValue(Promise.resolve(mockResult));
      spyOn(component, 'setLocalDeviceConnectionHistoryId');
      spyOn(component as any, 'setLoadingStatus');
      spyOn(component as any, 'defaultSelectAll');

      await component.loadAll(mockSearchRequestBody);

      expect((component as any).setLoadingStatus).toHaveBeenCalledWith(true);
      expect(component.deviceConnectionHistorySearchRequestBody).toEqual(mockSearchRequestBody);
      expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryList).toHaveBeenCalledWith(
        mockSearchRequestBody,
        { page: -1, size: component.itemsPerPage }
      );
      expect(component.deviceConnectionHistoryResponseList).toEqual(mockDeviceConnectionHistoryList);
      expect(component.totalRecordDisplay).toBe(2);
      expect(component.totalRecord).toBe(10);
      expect(component.totalItems).toBe(10);
      expect(component.page).toBe(1);
      expect(component.setLocalDeviceConnectionHistoryId).toHaveBeenCalledWith(mockDeviceConnectionHistoryList);
      expect((component as any).setLoadingStatus).toHaveBeenCalledWith(false);
      expect((component as any).defaultSelectAll).toHaveBeenCalled();
    });

    it('should handle failed data loading', async () => {
      const mockResult = {
        success: false,
        deviceConnectionHistoryList: [],
        totalRecordDisplay: 0,
        totalRecord: 0,
        totalItems: 0,
        page: 0,
        localDeviceConnectionHistoryList: []
      };

      deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryList.and.returnValue(Promise.resolve(mockResult));
      spyOn(component as any, 'setLoadingStatus');
      spyOn(component as any, 'defaultSelectAll');

      await component.loadAll(mockSearchRequestBody);

      expect(component.deviceConnectionHistoryResponseList).toEqual([]);
      expect(component.totalRecordDisplay).toBe(0);
      expect(component.totalRecord).toBe(0);
      expect(component.totalItems).toBe(0);
      expect((component as any).setLoadingStatus).toHaveBeenCalledWith(false);
      expect((component as any).defaultSelectAll).toHaveBeenCalled();
    });

    it('should set local device connection history id', () => {
      spyOn(component as any, 'defaultSelectAll');

      component.setLocalDeviceConnectionHistoryId(mockDeviceConnectionHistoryList);

      expect(component.localDeviceConnectionHistoryIdListArray).toEqual([1, 2]);
      expect((component as any).defaultSelectAll).toHaveBeenCalled();
    });

    it('should set loading status', () => {
      (component as any).setLoadingStatus(true);
      expect(component.loading).toBe(true);

      (component as any).setLoadingStatus(false);
      expect(component.loading).toBe(false);
    });
  });

  // ==================== NAVIGATION TESTS ====================
  describe('Navigation', () => {
    beforeEach(() => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      component.ngOnInit();
    });

    it('should show device connection history list', () => {
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.deviceConnectionHistoryId = 123;
      component.deviceConnectionHistoryListDisplay = false;
      component.deviceConnectionHistoryDetailDisplay = true;
      component.selectedDeviceConnectionHistoryIdList = [1, 2];
      component.isFilterHidden = true;

      component.showDeviceConnectionHistoryList();

      expect(component.isFilterComponentInitWithApicall).toBe(false);
      expect(component.listPageRefreshForbackToDetailPage).toBe(true);
      expect(component.deviceConnectionHistoryId).toBeNull();
      expect(component.deviceConnectionHistoryListDisplay).toBe(true);
      expect(component.deviceConnectionHistoryDetailDisplay).toBe(false);
      expect(component.selectedDeviceConnectionHistoryIdList).toEqual([]);
      expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
    });

    it('should not call filterPageSubjectCallForReloadPage when filter is not hidden', () => {
      spyOn(component, 'filterPageSubjectCallForReloadPage');

      component.isFilterHidden = false;

      component.showDeviceConnectionHistoryList();

      expect(component.filterPageSubjectCallForReloadPage).not.toHaveBeenCalled();
    });

    it('should show device connection history detail', () => {
      const testId = 456;

      component.showDeviceConnectionHistoryDetail(testId);

      expect(component.deviceConnectionHistoryId).toBe(testId);
      expect(component.deviceConnectionHistoryListDisplay).toBe(false);
      expect(component.deviceConnectionHistoryDetailDisplay).toBe(true);
    });

    it('should show probe connection list display', () => {
      spyOn(component.showProbeConnectionHistoryListDisplay, 'emit');

      component.showProbeConnectionListDisplay();

      expect(component.showProbeConnectionHistoryListDisplay.emit).toHaveBeenCalled();
    });
  });

  // ==================== CONSTANTS TESTS ====================
  describe('Constants and Properties', () => {
    it('should have correct constant values', () => {
      expect(component.chkPreFix).toBe('deviceConnectionHistory');
      expect(component.selectAllCheckboxId).toBe('selectAllDeviceConnection');
      expect(component.checkboxListName).toBe('deviceConnectionItem[]');
      expect(component.serialNumberOrHwId).toBeDefined();
      expect(component.deviceModel).toBeDefined();
      expect(component.manufacturer).toBeDefined();
      expect(component.osType).toBeDefined();
      expect(component.lastConnectedDate).toBeDefined();
    });

    it('should initialize with correct default values', () => {
      expect(component.loading).toBe(false);
      expect(component.itemsPerPage).toBe(0);
      expect(component.page).toBe(0);
      expect(component.previousPage).toBe(0);
      expect(component.totalItems).toBe(0);
      expect(component.totalRecordDisplay).toBe(0);
      expect(component.totalRecord).toBe(0);
      expect(component.dataSizes).toEqual([]);
      expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
      expect(component.isFilterComponentInitWithApicall).toBe(true);
      expect(component.listPageRefreshForbackToDetailPage).toBe(false);
      expect(component.isFilterHidden).toBe(true);
      expect(component.hideShowFilterButtonText).toBe(collapseFilterTextEnum.HIDE_FILTER);
      expect(component.deviceConnectionHistoryResponseList).toEqual([]);
      expect(component.connectioHistoryAdminPermission).toBe(false);
      expect(component.deviceConnectionHistoryDetailDisplay).toBe(false);
      expect(component.deviceConnectionHistoryListDisplay).toBe(false);
      expect(component.deviceConnectionHistoryId).toBeNull();
      expect(component.selectedDeviceConnectionHistoryIdList).toEqual([]);
      expect(component.localDeviceConnectionHistoryIdListArray).toEqual([]);
      expect(component.deviceConnectionHistorySearchRequestBody).toBeNull();
      expect(component.showCheckBox).toBe(false);
    });
  });

  // ==================== ERROR HANDLING TESTS ====================
  describe('Error Handling', () => {
    beforeEach(() => {
      authServiceSpy.isAuthenticate.and.returnValue(true);
      component.ngOnInit();
    });

    it('should handle API errors gracefully', async () => {
      const mockErrorResult = {
        success: false,
        deviceConnectionHistoryList: [],
        totalRecordDisplay: 0,
        totalRecord: 0,
        totalItems: 0,
        page: 0,
        localDeviceConnectionHistoryList: []
      };

      deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryList.and.returnValue(Promise.resolve(mockErrorResult));
      spyOn(component as any, 'setLoadingStatus');

      await component.loadAll(mockSearchRequestBody);

      expect(component.deviceConnectionHistoryResponseList).toEqual([]);
      expect(component.totalRecordDisplay).toBe(0);
      expect(component.totalRecord).toBe(0);
      expect(component.totalItems).toBe(0);
      expect((component as any).setLoadingStatus).toHaveBeenCalledWith(false);
    });

    it('should handle empty device connection history list', () => {
      spyOn(component as any, 'defaultSelectAll');

      component.setLocalDeviceConnectionHistoryId([]);

      expect(component.localDeviceConnectionHistoryIdListArray).toEqual([]);
      expect((component as any).defaultSelectAll).toHaveBeenCalled();
    });

    it('should handle checkbox selection with non-existent item', () => {
      component.selectedDeviceConnectionHistoryIdList = [1, 2, 3];
      spyOn(component as any, 'defaultSelectAll');

      const nonExistentItem = { ...mockDeviceConnectionHistoryResponse, id: 999 };

      component.selectCheckbox(nonExistentItem, false);

      // Should not affect the list since item doesn't exist
      expect(component.selectedDeviceConnectionHistoryIdList).toEqual([1, 2, 3]);
      expect((component as any).defaultSelectAll).toHaveBeenCalled();
    });
  });
});
