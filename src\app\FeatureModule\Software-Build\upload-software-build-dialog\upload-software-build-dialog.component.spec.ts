import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { UploadFileChunckProperty } from 'src/app/model/SoftwaarBuilds/UploadFileChunckProperty.model';
import { UploadPackageRequest } from 'src/app/model/upload.package.request';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { UserApiCallService } from 'src/app/shared/Service/user/user-api-call.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { UploadSoftwareBuildDialogComponent } from './upload-software-build-dialog.component';
import { SoftwareBuildApiCallService } from '../software-build-services/software-api-call/software-build-api-call.service';

describe('UploadSoftwareBuildDialogComponent', () => {
  let component: UploadSoftwareBuildDialogComponent;
  let fixture: ComponentFixture<UploadSoftwareBuildDialogComponent>;
  let softwareBuildApiCallServiceMock: any;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let activeModalSpy: jasmine.SpyObj<NgbActiveModal>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let validationServiceSpy: jasmine.SpyObj<ValidationService>;
  let exceptionHandlingServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;


  beforeEach(async () => {
    softwareBuildApiCallServiceMock = jasmine.createSpyObj('SoftwareBuildApiCallService', [
      'pushFileToStorage',
      'uploadFileToStorage',
      'commitFileToStorage',
      'updateFirmwareUploadStatus'
    ]);

    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    activeModalSpy = jasmine.createSpyObj('NgbActiveModal', ['close']);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['getIdsFromArray', 'getIdFromJsonObject', 'checkNullFieldValue', 'validateInput']);
    validationServiceSpy = jasmine.createSpyObj('ValidationService', ['removeSpaces']);
    exceptionHandlingServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);

    await TestBed.configureTestingModule({
      declarations: [UploadSoftwareBuildDialogComponent],
      imports: [FormsModule,
        ReactiveFormsModule,
        NgMultiSelectDropDownModule.forRoot()],
      providers: [
        { provide: NgbActiveModal, useValue: activeModalSpy },
        UserApiCallService,
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionHandlingServiceSpy },
        AuthJwtService,
        DownloadService,
        LocalStorageService,
        SessionStorageService,
        MultiSelectDropDownSettingService,
        { provide: ValidationService, useValue: validationServiceSpy },
        { provide: SoftwareBuildApiCallService, useValue: softwareBuildApiCallServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(UploadSoftwareBuildDialogComponent);
    component = fixture.componentInstance;

    // Inject @Input() data
    component.countryList = [{ id: 1, country: 'USA' } as CountryListResponse];
    component.jsonVersionList = [{ id: 123, name: 'v1' }];
    component.title = 'Upload';
    component.message = 'Test message';
    component.btnOkText = 'OK';
    component.btnCancelText = 'Cancel';

    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should handle file selection and call upload flow', () => {
    const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
    const jsonFile = new File(['{ "version": 1 }'], 'info.json', { type: 'application/json' });
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(zipFile);
    dataTransfer.items.add(jsonFile);
    const event = { target: { files: dataTransfer.files } };

    component.form.controls['Title'].setValue('Title123');
    component.form.controls['Version'].setValue('v1.0');
    component.form.controls['country'].setValue([{ countryId: 1 }]);
    component.form.controls['jsonVersion'].setValue([component.jsonVersionList[0]]);
    component.form.controls['partNumber'].setValue('PN1234');

    component.handleFileInput(event);
    expect(component.fileSelectedState).toBeTrue();

    const uploadResponse = {
      body: {
        preSignedUrlForAttachmentFile: 'https://fakeurl/attachment',
        preSignedUrlForReleaseNoteFile: 'https://fakeurl/release'
      }
    };

    softwareBuildApiCallServiceMock.pushFileToStorage.and.returnValue(of(uploadResponse));
    softwareBuildApiCallServiceMock.uploadFileToStorage.and.returnValue(of({}));
    softwareBuildApiCallServiceMock.commitFileToStorage.and.returnValue(of({ status: 201 }));
    softwareBuildApiCallServiceMock.updateFirmwareUploadStatus.and.returnValue(of({}));

    // ✅ Prevent infinite recursion
    spyOn<any>(component, 'uploadFileInChunk').and.callFake(() => {
      return;
    });

    component.accept();

    expect(softwareBuildApiCallServiceMock.pushFileToStorage).toHaveBeenCalled();
    expect(softwareBuildApiCallServiceMock.uploadFileToStorage).toHaveBeenCalled();
  });


  it('should handle upload error gracefully', () => {
    const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
    const jsonFile = new File(['{ "version": 1 }'], 'info.json', { type: 'application/json' });
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(zipFile);
    dataTransfer.items.add(jsonFile);

    component.form.controls['Title'].setValue('Title123');
    component.form.controls['Version'].setValue('v1.0');
    component.form.controls['country'].setValue([{ countryId: 1 }]);
    component.form.controls['jsonVersion'].setValue([component.jsonVersionList[0]]);
    component.form.controls['partNumber'].setValue('PN1234');
    component.selectedFiles = dataTransfer.files;

    softwareBuildApiCallServiceMock.pushFileToStorage.and.returnValue(throwError(() => new Error('Upload failed')));

    component.accept();

    expect(component['exceptionService'].customErrorMessage).toHaveBeenCalled();
  });

  it('should commit the file after uploading all chunks', () => {
    const dummyFile = new File(['abc'], 'dummy.txt', { type: 'text/plain' });
    const props = new UploadFileChunckProperty(0, 3, 3, dummyFile, 'block', []);
    const response = { preSignedUrlForAttachmentFile: 'https://fakeurl/attachment' };
    const request = null;

    component['encodeToBase64'] = (str: string) => btoa(str); // override for test

    softwareBuildApiCallServiceMock.commitFileToStorage.and.returnValue(of(new HttpResponse({ status: 201 })));
    softwareBuildApiCallServiceMock.updateFirmwareUploadStatus.and.returnValue(of(new HttpResponse({ body: {} })));

    component['uploadFileInChunk'](props, response as any, 'v1.0', request);

    expect(component['softwareBuildApiCallService'].commitFileToStorage).toHaveBeenCalled();
    expect(component['softwareBuildApiCallService'].updateFirmwareUploadStatus).toHaveBeenCalled();
    expect(component['activeModal'].close).toHaveBeenCalledWith(true);
  });

  it('should upload a file chunk when totalBytesRemaining is greater than 0', () => {
    const file = new File(['1234567890'], 'test.txt', { type: 'text/plain' }); // 10 bytes
    const props = new UploadFileChunckProperty(1000, 0, 5, file, 'block', []);
    const response = { preSignedUrlForAttachmentFile: 'https://fakeurl/attachment' };
    const requestData = { attachmentSize: 1056 } as UploadPackageRequest;

    component['encodeToBase64'] = (str: string) => btoa(str);

    // prevent actual recursion
    const recursiveSpy = spyOn(component as any, 'uploadFileInChunk').and.stub();
    softwareBuildApiCallServiceMock.uploadFileToStorage.and.returnValue(of(new HttpResponse({ status: 200, body: {} })));

    component['uploadFileInChunk'](props, response as any, 'v1.0', requestData);

    expect(recursiveSpy).toHaveBeenCalled();
  });

  it('should handle error if updateFirmwareUploadStatus fails after commit', () => {
    const dummyFile = new File(['abc'], 'dummy.txt', { type: 'text/plain' });
    const props = new UploadFileChunckProperty(0, 3, 3, dummyFile, 'block', ['dummyBlock']);
    const response = { preSignedUrlForAttachmentFile: 'https://fakeurl/attachment' };
    const requestData = {} as UploadPackageRequest;

    component['encodeToBase64'] = (str: string) => btoa(str);
    softwareBuildApiCallServiceMock.commitFileToStorage.and.returnValue(of(new HttpResponse({ status: 500 })));
    softwareBuildApiCallServiceMock.updateFirmwareUploadStatus.and.returnValue(of(new HttpResponse({ body: {} })));
    component['uploadFileInChunk'](props, response as any, 'v1.0', requestData);

    expect(component['softwareBuildApiCallService'].commitFileToStorage).toHaveBeenCalled();
  });

  // ==================== COMPONENT INITIALIZATION TESTS ====================

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.disableBtn).toBe(true);
      expect(component.fileValidation).toBe(false);
      expect(component.fileSelectedState).toBe(false);
      expect(component.uploadProgress).toBe(0);
      expect(component.uploadedData).toBe(0);
      expect(component.totalData).toBe(0);
      expect(component.progressVisible).toBe(false);
      expect(component.disabled).toBe(false);
    });

    it('should initialize form with validators', () => {
      expect(component.form).toBeDefined();
      expect(component.form.get('Title')).toBeDefined();
      expect(component.form.get('Version')).toBeDefined();
      expect(component.form.get('country')).toBeDefined();
      expect(component.form.get('jsonVersion')).toBeDefined();
      expect(component.form.get('partNumber')).toBeDefined();
      expect(component.form.get('fileSelector')).toBeDefined();
    });

    it('should set dropdown settings on ngOnInit', () => {
      component.ngOnInit();

      expect(component.countryDropdownSettings).toBeDefined();
      expect(component.dropdownSettingsForJsonVersion).toBeDefined();
    });

    it('should call updateCountryList on ngOnInit', () => {
      spyOn(component, 'updateCountryList' as any);

      component.ngOnInit();

      expect(component['updateCountryList']).toHaveBeenCalled();
    });
  });

  // ==================== INPUT PROPERTIES TESTS ====================

  describe('Input Properties', () => {
    it('should accept title input', () => {
      const testTitle = 'Upload Software Build';
      component.title = testTitle;
      expect(component.title).toBe(testTitle);
    });

    it('should accept message input', () => {
      const testMessage = 'Please select files to upload';
      component.message = testMessage;
      expect(component.message).toBe(testMessage);
    });

    it('should accept btnOkText input', () => {
      const testText = 'Upload';
      component.btnOkText = testText;
      expect(component.btnOkText).toBe(testText);
    });

    it('should accept btnCancelText input', () => {
      const testText = 'Cancel';
      component.btnCancelText = testText;
      expect(component.btnCancelText).toBe(testText);
    });

    it('should accept countryList input', () => {
      const testCountryList = [{ id: 1, country: 'USA' } as CountryListResponse];
      component.countryList = testCountryList;
      expect(component.countryList).toBe(testCountryList);
    });

    it('should accept jsonVersionList input', () => {
      const testJsonList = [{ id: 1, name: 'v1.0' }];
      component.jsonVersionList = testJsonList;
      expect(component.jsonVersionList).toBe(testJsonList);
    });
  });

  // ==================== FILE HANDLING TESTS ====================

  describe('File Handling', () => {
    it('should handle valid file selection (zip + json)', () => {
      const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
      const jsonFile = new File(['{ "version": 1 }'], 'info.json', { type: 'application/json' });
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(zipFile);
      dataTransfer.items.add(jsonFile);
      const event = { target: { files: dataTransfer.files } };

      component.form.patchValue({
        Title: 'Test Title',
        Version: 'v1.0',
        country: [{ id: 1 }],
        partNumber: 'PN123'
      });

      component.handleFileInput(event);

      expect(component.selectedFiles).toBe(dataTransfer.files);
      expect(component.fileValidation).toBe(false);
      expect(component.fileSelectedState).toBe(true);
    });

    it('should handle invalid file selection (wrong types)', () => {
      const txtFile = new File(['dummy text'], 'test.txt', { type: 'text/plain' });
      const pdfFile = new File(['dummy pdf'], 'test.pdf', { type: 'application/pdf' });
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(txtFile);
      dataTransfer.items.add(pdfFile);
      const event = { target: { files: dataTransfer.files } };

      component.handleFileInput(event);

      expect(component.disableBtn).toBe(true);
      expect(component.fileValidation).toBe(true);
      expect(component.fileSelectedState).toBe(false);
    });

    it('should handle insufficient file count', () => {
      const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(zipFile);
      const event = { target: { files: dataTransfer.files } };

      component.handleFileInput(event);

      expect(component.disableBtn).toBe(true);
      expect(component.fileValidation).toBe(true);
      expect(component.fileSelectedState).toBe(false);
    });

    it('should handle excessive file count', () => {
      const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
      const jsonFile = new File(['{ "version": 1 }'], 'info.json', { type: 'application/json' });
      const txtFile = new File(['dummy text'], 'test.txt', { type: 'text/plain' });
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(zipFile);
      dataTransfer.items.add(jsonFile);
      dataTransfer.items.add(txtFile);
      const event = { target: { files: dataTransfer.files } };

      component.handleFileInput(event);

      expect(component.disableBtn).toBe(true);
      expect(component.fileValidation).toBe(true);
      expect(component.fileSelectedState).toBe(false);
    });
  });

  // ==================== BUTTON DISABLE TESTS ====================

  describe('Button Disable Logic', () => {
    it('should enable button when form is valid and files are selected', () => {
      const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
      const jsonFile = new File(['{ "version": 1 }'], 'info.json', { type: 'application/json' });
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(zipFile);
      dataTransfer.items.add(jsonFile);

      component.selectedFiles = dataTransfer.files;
      component.form.patchValue({
        Title: 'Test Title',
        Version: 'v1.0',
        country: [{ id: 1 }],
        partNumber: 'PN123'
      });

      component.buttonDisable(null);

      expect(component.disableBtn).toBe(true);
    });

    it('should handle undefined selectedFiles', () => {
      component.selectedFiles = undefined;

      component.buttonDisable(null);

      expect(component.fileValidation).toBe(false);
    });

    it('should handle incorrect file count in buttonDisable', () => {
      const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(zipFile);

      component.selectedFiles = dataTransfer.files;

      component.buttonDisable(null);

      expect(component.fileValidation).toBe(false);
    });
  });

  // ==================== MODAL ACTIONS TESTS ====================

  describe('Modal Actions', () => {
    it('should close modal with false when decline is called', () => {
      component.decline();

      expect(activeModalSpy.close).toHaveBeenCalledWith(false);
    });

    it('should call confirmDownloadDataset when accept is called', () => {
      spyOn(component, 'confirmDownloadDataset' as any);

      component.form.patchValue({
        Title: 'Test Title',
        Version: 'v1.0',
        country: [{ id: 1 }],
        jsonVersion: [{ id: 1 }],
        partNumber: 'PN123'
      });

      const zipFile = new File(['dummy zip'], 'test.zip', { type: 'application/zip' });
      const jsonFile = new File(['{ "version": 1 }'], 'info.json', { type: 'application/json' });
      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(zipFile);
      dataTransfer.items.add(jsonFile);
      component.selectedFiles = dataTransfer.files;

      commonsServiceSpy.getIdsFromArray.and.returnValue([1]);
      commonsServiceSpy.checkNullFieldValue.and.returnValue('PN123');

      component.accept();

      expect(component['confirmDownloadDataset']).toHaveBeenCalled();
    });
  });

  // ==================== UTILITY METHODS TESTS ====================

  describe('Utility Methods', () => {
    it('should encode string to base64', () => {
      const testString = 'test-string';
      const result = component['encodeToBase64'](testString);

      expect(result).toBe(btoa(testString));
    });

    it('should pad numbers correctly', () => {
      expect(component['pad'](5, 3)).toBe('005');
      expect(component['pad'](123, 5)).toBe('00123');
      expect(component['pad'](1234, 3)).toBe('1234');
    });

    it('should set loading status', () => {
      spyOn(component['downloadService'], 'setLoading');

      component['setLoadingStatus'](true);

      expect(component['downloadService'].setLoading).toHaveBeenCalledWith(true, null);
    });

    it('should update country list when not null', () => {
      component.countryList = [
        { id: 1, country: 'USA', isDisabled: true } as CountryListResponse,
        { id: 2, country: 'Canada', isDisabled: true } as CountryListResponse
      ];

      component['updateCountryList']();

      expect(component.countryList[0].isDisabled).toBe(false);
      expect(component.countryList[1].isDisabled).toBe(false);
    });

    it('should handle null country list in updateCountryList', () => {
      component.countryList = null;

      expect(() => component['updateCountryList']()).not.toThrow();
    });
  });
});
