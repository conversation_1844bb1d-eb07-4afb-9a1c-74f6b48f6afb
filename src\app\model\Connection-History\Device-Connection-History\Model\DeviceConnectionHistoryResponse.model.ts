import { ConnectionTypeEnum } from "src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum";
import { OSTypeEnum } from "src/app/shared/enum/Probe/OSTypeEnum.enum";

export class DeviceConnectionHistoryResponse {
    id: number;
    manufacturer: string;
    deviceModel: string;
    deviceSerialNumber: string;
    osType: OSTypeEnum;
    connectionType: ConnectionTypeEnum;
    lastConnectedDate: number; // Epoch milliseconds
}