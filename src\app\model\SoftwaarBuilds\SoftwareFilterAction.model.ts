import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { SoftwareBuildSearchRequestBody } from "./SoftwareBuildSearchRequestBody";

export class SoftwareBuildFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    softwareBuildSearchRequestBody: SoftwareBuildSearchRequestBody;


    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $softwareBuildSearchRequestBody: SoftwareBuildSearchRequestBody) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.softwareBuildSearchRequestBody = $softwareBuildSearchRequestBody;
    }
}