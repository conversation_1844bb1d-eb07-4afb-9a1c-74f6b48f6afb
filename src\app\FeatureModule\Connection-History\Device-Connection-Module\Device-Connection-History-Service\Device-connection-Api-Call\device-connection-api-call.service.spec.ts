import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { DeviceConnectionApiCallService } from './device-connection-api-call.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfigInjectService } from 'src/app/shared/InjectService/config-inject.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistorySearchRequestBody.model';
import { DeviceConnectionHistoryPegableResponse } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryPegableResponse.mode';
import { DeviceConnectionHistoryResponse } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryResponse.model';
import { OSTypeEnum } from 'src/app/shared/enum/Probe/OSTypeEnum.enum';
import { ConnectionTypeEnum } from 'src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum';
import { Pageable } from 'src/app/model/common/pageable.model';
import { Sort } from 'src/app/model/common/sort.model';
import { HttpErrorResponse } from '@angular/common/http';
import { of, throwError } from 'rxjs';

describe('DeviceConnectionApiCallService', () => {
  let service: DeviceConnectionApiCallService;
  let httpMock: HttpTestingController;
  let configInjectServiceSpy: jasmine.SpyObj<ConfigInjectService>;
  let exceptionServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;

  // Mock data
  const mockDeviceConnectionHistoryResponse: DeviceConnectionHistoryResponse = {
    id: 1,
    deviceSerialNumber: 'TEST123',
    deviceModel: 'TestModel',
    manufacturer: 'TestManufacturer',
    osType: OSTypeEnum.BRIDGE,
    connectionType: ConnectionTypeEnum.INTERNAL,
    lastConnectedDate: 1672531200000
  };

  const mockDeviceConnectionHistoryList: DeviceConnectionHistoryResponse[] = [
    mockDeviceConnectionHistoryResponse,
    { ...mockDeviceConnectionHistoryResponse, id: 2, deviceSerialNumber: 'TEST456' }
  ];

  const mockSort = new Sort(false, true, false);
  const mockPageable = new Pageable(mockSort, 0, 10, 0, true, false);
  const mockPagableResponse: DeviceConnectionHistoryPegableResponse = new DeviceConnectionHistoryPegableResponse(
    mockPageable,
    1,
    false,
    10,
    2,
    true,
    mockSort,
    10,
    0,
    false,
    mockDeviceConnectionHistoryList
  );

  const mockSearchRequestBody = new DeviceConnectionHistorySearchRequestBody(
    'TestManufacturer',
    'TestModel',
    'TEST123',
    null,
    1672531200000
  );

  const mockServerUrl = 'https://localhost:8080/';

  beforeEach(() => {
    // Create service spies
    configInjectServiceSpy = jasmine.createSpyObj('ConfigInjectService', ['getServerApiUrl']);
    exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['handleError']);

    // Set up default return values
    configInjectServiceSpy.getServerApiUrl.and.returnValue(mockServerUrl);
    commonsServiceSpy.handleError.and.returnValue(throwError(() => new Error('Handled error')));

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        DeviceConnectionApiCallService,
        { provide: ConfigInjectService, useValue: configInjectServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        LocalStorageService,
        SessionStorageService,
        commonsProviders(null)
      ]
    });

    service = TestBed.inject(DeviceConnectionApiCallService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  // ==================== INITIALIZATION TESTS ====================
  describe('Service Initialization', () => {
    it('should initialize with correct API URL', () => {
      expect(configInjectServiceSpy.getServerApiUrl).toHaveBeenCalled();
      expect(service.deviceConnectionHistory).toBe(mockServerUrl + 'api/connection-history');
    });

    it('should have correct API endpoint', () => {
      expect(service.deviceConnectionHistory).toContain('api/connection-history');
    });
  });

  // ==================== API CALL TESTS ====================
  describe('API Calls', () => {
    it('should get device connection history list successfully', () => {
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe(response => {
        expect(response.status).toBe(200);
        expect(response.body).toEqual(mockPagableResponse);
      });

      const req = httpMock.expectOne(request => {
        return request.url === service.deviceConnectionHistory + '/devices/search' &&
          request.method === 'POST';
      });

      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockSearchRequestBody);
      expect(req.request.params.get('page')).toBe('0');
      expect(req.request.params.get('size')).toBe('10');

      req.flush(mockPagableResponse);
    });

    it('should handle API call with empty request body', () => {
      const emptyRequestBody = new DeviceConnectionHistorySearchRequestBody(null, null, null, null, null);
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(emptyRequestBody, requestParams).subscribe(response => {
        expect(response.status).toBe(200);
        expect(response.body).toEqual(mockPagableResponse);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      expect(req.request.body).toEqual(emptyRequestBody);
      req.flush(mockPagableResponse);
    });

    it('should handle API call with different pagination parameters', () => {
      const requestParams = { page: 2, size: 25, sort: ['id,desc'] };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(request => {
        return request.url === service.deviceConnectionHistory + '/devices/search';
      });

      expect(req.request.params.get('page')).toBe('2');
      expect(req.request.params.get('size')).toBe('25');
      expect(req.request.params.getAll('sort')).toEqual(['id,desc']);

      req.flush(mockPagableResponse);
    });

    it('should handle API call with null request parameters', () => {
      service.getDeviceConnectionHistoryList(mockSearchRequestBody, null).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      expect(req.request.params.keys().length).toBe(0);
      req.flush(mockPagableResponse);
    });

    it('should handle API call with undefined request parameters', () => {
      service.getDeviceConnectionHistoryList(mockSearchRequestBody, undefined).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      expect(req.request.params.keys().length).toBe(0);
      req.flush(mockPagableResponse);
    });
  });

  // ==================== ERROR HANDLING TESTS ====================
  describe('Error Handling', () => {
    it('should handle HTTP 404 error', () => {
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error).toBeDefined();
        }
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });

    it('should handle HTTP 500 error', () => {
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error).toBeDefined();
        }
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      req.flush('Internal Server Error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('should handle network error', () => {
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error).toBeDefined();
        }
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      req.error(new ProgressEvent('Network error'));
    });

    it('should handle timeout error', () => {
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error).toBeDefined();
        }
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      req.error(new ProgressEvent('Timeout'));
    });

    it('should use commons service error handler', () => {
      const requestParams = { page: 0, size: 10 };

      // The error handler is called through the pipe(catchError(...)) in the service
      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.message).toBe('Handled error');
        }
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      req.flush('Server Error', { status: 500, statusText: 'Internal Server Error' });
    });
  });

  // ==================== REQUEST PARAMETER TESTS ====================
  describe('Request Parameters', () => {
    it('should handle complex request parameters', () => {
      const complexParams = {
        page: 5,
        size: 50,
        sort: ['lastConnectedDate,desc', 'id,asc'],
        filter: 'active',
        search: 'test'
      };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, complexParams).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(request => {
        return request.url === service.deviceConnectionHistory + '/devices/search';
      });

      expect(req.request.params.get('page')).toBe('5');
      expect(req.request.params.get('size')).toBe('50');
      expect(req.request.params.get('filter')).toBe('active');
      expect(req.request.params.get('search')).toBe('test');
      expect(req.request.params.getAll('sort')).toEqual(['lastConnectedDate,desc', 'id,asc']);

      req.flush(mockPagableResponse);
    });

    it('should handle empty object as request parameters', () => {
      const emptyParams = {};

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, emptyParams).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      expect(req.request.params.keys().length).toBe(0);
      req.flush(mockPagableResponse);
    });

    it('should handle request parameters with zero values', () => {
      const zeroParams = { page: 0, size: 0 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, zeroParams).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      expect(req.request.params.get('page')).toBe('0');
      expect(req.request.params.get('size')).toBe('0');
      req.flush(mockPagableResponse);
    });

    it('should handle request parameters with string values', () => {
      const stringParams = { page: '3', size: '20', customParam: 'value' };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, stringParams).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      expect(req.request.params.get('page')).toBe('3');
      expect(req.request.params.get('size')).toBe('20');
      expect(req.request.params.get('customParam')).toBe('value');
      req.flush(mockPagableResponse);
    });
  });

  // ==================== RESPONSE HANDLING TESTS ====================
  describe('Response Handling', () => {
    it('should handle successful response with full data', () => {
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe(response => {
        expect(response.status).toBe(200);
        expect(response.body).toEqual(mockPagableResponse);
        expect(response.body.content).toEqual(mockDeviceConnectionHistoryList);
        expect(response.body.totalElements).toBe(10);
        expect(response.body.numberOfElements).toBe(2);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      req.flush(mockPagableResponse);
    });

    it('should handle response with empty content', () => {
      const emptyResponse = new DeviceConnectionHistoryPegableResponse(
        mockPageable, 0, true, 0, 0, true, mockSort, 10, 0, true, []
      );
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe(response => {
        expect(response.status).toBe(200);
        expect(response.body.content).toEqual([]);
        expect(response.body.totalElements).toBe(0);
        expect(response.body.numberOfElements).toBe(0);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      req.flush(emptyResponse);
    });

    it('should handle response with different status codes', () => {
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe(response => {
        expect(response.status).toBe(201);
        expect(response.body).toEqual(mockPagableResponse);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      req.flush(mockPagableResponse, { status: 201, statusText: 'Created' });
    });
  });

  // ==================== INTEGRATION TESTS ====================
  describe('Integration Tests', () => {
    it('should handle complete API workflow', () => {
      const requestParams = { page: 1, size: 25 };
      let responseReceived = false;

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams).subscribe({
        next: (response) => {
          responseReceived = true;
          expect(response.status).toBe(200);
          expect(response.body).toEqual(mockPagableResponse);
        },
        error: () => fail('Should not have failed')
      });

      const req = httpMock.expectOne(request => {
        return request.url === service.deviceConnectionHistory + '/devices/search' &&
          request.method === 'POST' &&
          request.body === mockSearchRequestBody;
      });

      expect(req.request.params.get('page')).toBe('1');
      expect(req.request.params.get('size')).toBe('25');

      req.flush(mockPagableResponse);
      expect(responseReceived).toBe(true);
    });

    it('should handle multiple concurrent requests', () => {
      const requestParams1 = { page: 0, size: 10 };
      const requestParams2 = { page: 1, size: 20 };

      let response1Received = false;
      let response2Received = false;

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams1).subscribe(() => {
        response1Received = true;
      });

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, requestParams2).subscribe(() => {
        response2Received = true;
      });

      const requests = httpMock.match(service.deviceConnectionHistory + '/devices/search');
      expect(requests.length).toBe(2);

      requests[0].flush(mockPagableResponse);
      requests[1].flush(mockPagableResponse);

      expect(response1Received).toBe(true);
      expect(response2Received).toBe(true);
    });
  });

  // ==================== EDGE CASE TESTS ====================
  describe('Edge Cases', () => {
    it('should handle very large page numbers', () => {
      const largePageParams = { page: 999999, size: 1 };

      service.getDeviceConnectionHistoryList(mockSearchRequestBody, largePageParams).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      expect(req.request.params.get('page')).toBe('999999');
      req.flush(mockPagableResponse);
    });

    it('should handle special characters in request body', () => {
      const specialCharRequestBody = new DeviceConnectionHistorySearchRequestBody(
        'Manufacturer™',
        'Model®',
        'Serial#123',
        null,
        1672531200000
      );
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(specialCharRequestBody, requestParams).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      expect(req.request.body).toEqual(specialCharRequestBody);
      req.flush(mockPagableResponse);
    });

    it('should handle request with all null values in search body', () => {
      const nullRequestBody = new DeviceConnectionHistorySearchRequestBody(null, null, null, null, null);
      const requestParams = { page: 0, size: 10 };

      service.getDeviceConnectionHistoryList(nullRequestBody, requestParams).subscribe(response => {
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
      expect(req.request.body).toEqual(nullRequestBody);
      req.flush(mockPagableResponse);
    });
  });
});
