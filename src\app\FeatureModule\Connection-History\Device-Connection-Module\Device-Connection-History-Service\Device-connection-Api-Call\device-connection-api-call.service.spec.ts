import { TestBed } from '@angular/core/testing';

import { DeviceConnectionApiCallService } from './device-connection-api-call.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';

describe('DeviceConnectionApiCallService', () => {
  let service: DeviceConnectionApiCallService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        LocalStorageService,
        SessionStorageService,
        commonsProviders(null)
      ]
    });
    service = TestBed.inject(DeviceConnectionApiCallService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
