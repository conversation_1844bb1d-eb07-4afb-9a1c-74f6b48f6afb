import { TestBed } from '@angular/core/testing';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { DatePipe } from '@angular/common';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';

import { SoftwareBuildOperationsService } from './software-build-operations.service';
import { SoftwareBuildApiCallService } from './software-api-call/software-build-api-call.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';
import { SoftWareBuildConformationService } from './conformation-software-build-dialog/software-build-conformation.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { VideoService } from 'src/app/shared/videoservice/video.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils'
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { SoftwareBuildFilterAction } from 'src/app/model/SoftwaarBuilds/SoftwareFilterAction.model';
import { SoftwareBuildListResponse } from 'src/app/model/SoftwaarBuilds/SoftwareBuildListResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { PERMISSION_ERROR_MESSAGE, SoftwareBuildListResource } from 'src/app/app.constants';

describe('SoftwareBuildOperationsService', () => {
  let service: SoftwareBuildOperationsService;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let softwareBuildApiCallServiceSpy: jasmine.SpyObj<SoftwareBuildApiCallService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let exceptionHandlingServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;
  let moduleValidationServiceSpy: jasmine.SpyObj<ModuleValidationServiceService>;
  let softWareBuildConformationServiceSpy: jasmine.SpyObj<SoftWareBuildConformationService>;
  let videoServiceSpy: jasmine.SpyObj<VideoService>;
  let datePipeSpy: jasmine.SpyObj<DatePipe>;

  beforeEach(() => {
    toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    softwareBuildApiCallServiceSpy = jasmine.createSpyObj('SoftwareBuildApiCallService', [
      'inventoryList', 'deleteSoftwearBuild', 'getAttachmentUrl', 'mapInventoryWithDeviceType', 'markInventoriesActiveInactive'
    ]);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getSoftwearBuildPermission']);
    exceptionHandlingServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    moduleValidationServiceSpy = jasmine.createSpyObj('ModuleValidationServiceService', ['validateWithUserCountryForMultileRecord']);
    softWareBuildConformationServiceSpy = jasmine.createSpyObj('SoftWareBuildConformationService', ['openInventoryOperationModel']);
    videoServiceSpy = jasmine.createSpyObj('VideoService', ['downloadJSONFile']);
    datePipeSpy = jasmine.createSpyObj('DatePipe', ['transform']);

    TestBed.configureTestingModule({
      providers: [
        SoftwareBuildOperationsService,
        { provide: SoftwareBuildApiCallService, useValue: softwareBuildApiCallServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionHandlingServiceSpy },
        { provide: ModuleValidationServiceService, useValue: moduleValidationServiceSpy },
        { provide: SoftWareBuildConformationService, useValue: softWareBuildConformationServiceSpy },
        CommonsService,
        { provide: VideoService, useValue: videoServiceSpy },
        { provide: DatePipe, useValue: datePipeSpy },
        LocalStorageService,
        SessionStorageService,
        commonsProviders(toastrServiceSpy)
      ]
    });
    service = TestBed.inject(SoftwareBuildOperationsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  // ==================== SUBJECT METHODS TESTS ====================

  describe('Subject Methods', () => {
    it('should return software build list loading subject', () => {
      const subject = service.getSoftwareBuildListLoadingSubject();
      expect(subject).toBeDefined();
    });

    it('should call software build list loading subject with status', () => {
      const subject = service.getSoftwareBuildListLoadingSubject();
      spyOn(subject, 'next');

      service.callSoftwareBuildListLoadingSubject(true);
      expect(subject.next).toHaveBeenCalledWith(true);
    });

    it('should call loading subject when resource name matches SoftwareBuildListResource', () => {
      const subject = service.getSoftwareBuildListLoadingSubject();
      spyOn(subject, 'next');

      service.isLoading(true, SoftwareBuildListResource);
      expect(subject.next).toHaveBeenCalledWith(true);
    });

    it('should not call loading subject when resource name does not match', () => {
      const subject = service.getSoftwareBuildListLoadingSubject();
      spyOn(subject, 'next');

      service.isLoading(true, 'OTHER_RESOURCE');
      expect(subject.next).not.toHaveBeenCalled();
    });

    it('should return software build list refresh subject', () => {
      const subject = service.getSoftwareBuildListRefreshSubject();
      expect(subject).toBeDefined();
    });

    it('should call software build list filter request parameter subject', () => {
      const subject = service.getSoftwareBuildListFilterRequestParameterSubject();
      spyOn(subject, 'next');

      const filterAction = new SoftwareBuildFilterAction(
        new ListingPageReloadSubjectParameter(true, true, false, false),
        new SoftwareBuildSearchRequestBody("", [], null, null, null, null)
      );

      service.callSoftwareBuildListFilterRequestParameterSubject(filterAction);
      expect(subject.next).toHaveBeenCalledWith(filterAction);
    });

    it('should return software build list filter request parameter subject', () => {
      const subject = service.getSoftwareBuildListFilterRequestParameterSubject();
      expect(subject).toBeDefined();
    });
  });

  // ==================== CACHE METHODS TESTS ====================

  describe('Cache Methods', () => {
    it('should set and get json version list', () => {
      const jsonList: Jsonlist[] = [
        new Jsonlist(1, 'Version 1'),
        new Jsonlist(2, 'Version 2')
      ];

      service.setJsonVersionList(jsonList);
      expect(service.getJsonVersionList()).toEqual(jsonList);
    });

    it('should set and get country list', () => {
      const countryList: CountryListResponse[] = [
        new CountryListResponse(1, 'USA', ['English']),
        new CountryListResponse(2, 'Canada', ['English', 'French'])
      ];

      service.setCountryList(countryList);
      expect(service.getCountryList()).toEqual(countryList);
    });
  });

  // ==================== REFRESH PAGE SUBJECT TESTS ====================

  describe('Refresh Page Subject', () => {
    it('should call refresh page subject with SoftwareBuildListResource and clear filter', () => {
      const subject = service.getSoftwareBuildListFilterRequestParameterSubject();
      spyOn(subject, 'next');

      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const softwareBuildListFilterRequestBodyApply = new SoftwareBuildSearchRequestBody("test", [], null, null, null, null);

      service.callRefreshPageSubject(listingPageReloadSubjectParameter, SoftwareBuildListResource, false, softwareBuildListFilterRequestBodyApply);

      expect(subject.next).toHaveBeenCalled();
    });

    it('should call refresh page subject with clear filter true', () => {
      const subject = service.getSoftwareBuildListFilterRequestParameterSubject();
      spyOn(subject, 'next');

      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      listingPageReloadSubjectParameter.isClearFilter = true;

      service.callRefreshPageSubject(listingPageReloadSubjectParameter, SoftwareBuildListResource, false, null);

      expect(subject.next).toHaveBeenCalled();
    });

    it('should not call refresh page subject with different resource name', () => {
      const subject = service.getSoftwareBuildListFilterRequestParameterSubject();
      spyOn(subject, 'next');

      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      service.callRefreshPageSubject(listingPageReloadSubjectParameter, 'OTHER_RESOURCE', false, null);

      expect(subject.next).not.toHaveBeenCalled();
    });
  });

  // ==================== LOAD SOFTWARE BUILD LIST TESTS ====================

  describe('loadSoftwareBuildList', () => {
    const mockSearchRequestBody = new SoftwareBuildSearchRequestBody("test", [], null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };

    it('should load software build list successfully with permission', async () => {
      // Mock the response as the service expects it (casted to SoftwareBuildPageResponse)
      const mockResponseBody = {
        content: [
          { id: 1, version: 'v1.0', createdDate: 1640995200000, countries: ['USA'] }
        ],
        numberOfElements: 1,
        totalElements: 10,
        number: 0
      } as any; // Cast to any to bypass type checking for test

      const mockResponse = new HttpResponse({
        status: 200,
        body: mockResponseBody
      });

      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softwareBuildApiCallServiceSpy.inventoryList.and.returnValue(of(mockResponse as any));
      datePipeSpy.transform.and.returnValue('Jan 1, 2022, 12:00:00 AM');

      const result = await service.loadSoftwareBuildList(mockSearchRequestBody, mockPageObj);

      expect(result.success).toBe(true);
      expect(result.softwareBuilds.length).toBe(1);
      expect(result.totalSoftwareBuilds).toBe(10);
      expect(result.page).toBe(1);
      expect(permissionServiceSpy.getSoftwearBuildPermission).toHaveBeenCalledWith(PermissionAction.GET_SOFTWARE_BUILD_ACTION);
    });

    it('should return empty result when no permission', async () => {
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(false);

      const result = await service.loadSoftwareBuildList(mockSearchRequestBody, mockPageObj);

      expect(result.success).toBe(false);
      expect(result.softwareBuilds.length).toBe(0);
      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Insufficient permissions to load software build list');
    });

    it('should return empty result when response status is not 200', async () => {
      const mockResponse = new HttpResponse({
        status: 404,
        body: null
      });

      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softwareBuildApiCallServiceSpy.inventoryList.and.returnValue(of(mockResponse as any));

      const result = await service.loadSoftwareBuildList(mockSearchRequestBody, mockPageObj);

      expect(result.success).toBe(false);
      expect(result.softwareBuilds.length).toBe(0);
    });

    it('should handle API error and return empty result', async () => {
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Internal Server Error' });

      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softwareBuildApiCallServiceSpy.inventoryList.and.returnValue(throwError(() => mockError));

      const result = await service.loadSoftwareBuildList(mockSearchRequestBody, mockPageObj);

      expect(result.success).toBe(false);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(mockError);
    });

    it('should process dates correctly when createdDate exists', async () => {
      const mockResponseBody = {
        content: [
          { id: 1, version: 'v1.0', createdDate: 1640995200000 },
          { id: 2, version: 'v2.0', createdDate: null }
        ],
        numberOfElements: 2,
        totalElements: 2,
        number: 0
      } as any;

      const mockResponse = new HttpResponse({
        status: 200,
        body: mockResponseBody
      });

      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softwareBuildApiCallServiceSpy.inventoryList.and.returnValue(of(mockResponse as any));
      datePipeSpy.transform.and.returnValue('Jan 1, 2022, 12:00:00 AM');

      const result = await service.loadSoftwareBuildList(mockSearchRequestBody, mockPageObj);

      expect(datePipeSpy.transform).toHaveBeenCalledWith(new Date(1640995200000), 'MMM d, y, h:mm:ss a');
      expect(result.softwareBuilds[0].createdDate).toBe('Jan 1, 2022, 12:00:00 AM');
      expect(result.softwareBuilds[1].createdDate).toBeNull();
    });
  });

  // ==================== DOWNLOAD ATTACHMENT TESTS ====================

  describe('downloadAttachment', () => {
    it('should download attachment successfully', async () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: { urlToDownload: 'https://example.com/download-url' }
      });

      softwareBuildApiCallServiceSpy.getAttachmentUrl.and.returnValue(of(mockResponse as any));

      const result = await service.downloadAttachment(1, 'attachment', SoftwareBuildListResource);

      expect(result).toBe('https://example.com/download-url');
      expect(softwareBuildApiCallServiceSpy.getAttachmentUrl).toHaveBeenCalledWith(1, { attachmentType: 'attachment' });
    });

    it('should handle download attachment error', async () => {
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Internal Server Error' });

      softwareBuildApiCallServiceSpy.getAttachmentUrl.and.returnValue(throwError(() => mockError));

      try {
        await service.downloadAttachment(1, 'releaseNote', SoftwareBuildListResource);
        fail('Expected error to be thrown');
      } catch (error) {
        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(mockError);
      }
    });
  });

  // ==================== DELETE SOFTWARE BUILD TESTS ====================

  describe('deleteSoftwareBuild', () => {
    it('should delete software build successfully with confirmation', async () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: { message: 'Software build deleted successfully' }
      });

      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(true));
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softwareBuildApiCallServiceSpy.deleteSoftwearBuild.and.returnValue(of(mockResponse as any));

      const result = await service.deleteSoftwareBuild(1, 'SW-001', SoftwareBuildListResource);

      expect(result).toBe(true);
      expect(softWareBuildConformationServiceSpy.openInventoryOperationModel).toHaveBeenCalled();
      expect(permissionServiceSpy.getSoftwearBuildPermission).toHaveBeenCalledWith(PermissionAction.DELETE_SOFTWARE_BUILD_ACTION);
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Software build deleted successfully');
    });

    it('should return false when user cancels confirmation', async () => {
      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(false));

      const result = await service.deleteSoftwareBuild(1, 'SW-001', SoftwareBuildListResource);

      expect(result).toBe(false);
      expect(softwareBuildApiCallServiceSpy.deleteSoftwearBuild).not.toHaveBeenCalled();
    });

    it('should return false when no permission', async () => {
      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(true));
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(false);

      const result = await service.deleteSoftwareBuild(1, 'SW-001', SoftwareBuildListResource);

      expect(result).toBe(false);
      expect(toastrServiceSpy.error).toHaveBeenCalledWith(PERMISSION_ERROR_MESSAGE);
    });

    it('should handle delete error', async () => {
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Internal Server Error' });

      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(true));
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softwareBuildApiCallServiceSpy.deleteSoftwearBuild.and.returnValue(throwError(() => mockError));

      const result = await service.deleteSoftwareBuild(1, 'SW-001', SoftwareBuildListResource);

      expect(result).toBe(false);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(mockError);
    });

    it('should handle non-200 response status', async () => {
      const mockResponse = new HttpResponse({
        status: 400,
        body: { message: 'Bad request' }
      });

      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(true));
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softwareBuildApiCallServiceSpy.deleteSoftwearBuild.and.returnValue(of(mockResponse as any));

      const result = await service.deleteSoftwareBuild(1, 'SW-001', SoftwareBuildListResource);

      expect(result).toBe(true); // Still returns true as per service logic
      expect(toastrServiceSpy.success).not.toHaveBeenCalled();
    });
  });

  // ==================== CHANGE OPERATION TESTS ====================

  describe('changeOperationForSoftwareBuild', () => {
    const mockSoftwareBuilds: SoftwareBuildListResponse[] = [
      { id: 1, version: 'v1.0', countries: ['USA'] } as SoftwareBuildListResponse
    ];

    it('should show info message when no software builds selected', () => {
      service.changeOperationForSoftwareBuild('Map to Client Devices', SoftwareBuildListResource, [], []);

      expect(toastrServiceSpy.info).toHaveBeenCalledWith('Please Select Software Build(s)');
    });

    it('should call mapWithDeviceType for Map to Client Devices operation', async () => {
      spyOn(service, 'mapWithDeviceType').and.returnValue(Promise.resolve(true));
      spyOn(service as any, 'showSuccessAndRefresh');

      service.changeOperationForSoftwareBuild('Map to Client Devices', SoftwareBuildListResource, [1], mockSoftwareBuilds);

      await new Promise(resolve => setTimeout(resolve, 0)); // Wait for async operation

      expect(service.mapWithDeviceType).toHaveBeenCalledWith([1], mockSoftwareBuilds, [deviceTypesEnum.CLIENT_DEVICE], SoftwareBuildListResource);
    });

    it('should call mapWithDeviceType for Map to Demo Devices operation', async () => {
      spyOn(service, 'mapWithDeviceType').and.returnValue(Promise.resolve(true));
      spyOn(service as any, 'showSuccessAndRefresh');

      service.changeOperationForSoftwareBuild('Map to Demo Devices', SoftwareBuildListResource, [1], mockSoftwareBuilds);

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(service.mapWithDeviceType).toHaveBeenCalledWith([1], mockSoftwareBuilds, [deviceTypesEnum.DEMO_DEVICE], SoftwareBuildListResource);
    });

    it('should call mapWithDeviceType for Map to Both type of Devices operation', async () => {
      spyOn(service, 'mapWithDeviceType').and.returnValue(Promise.resolve(true));
      spyOn(service as any, 'showSuccessAndRefresh');

      service.changeOperationForSoftwareBuild('Map to Both type of Devices', SoftwareBuildListResource, [1], mockSoftwareBuilds);

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(service.mapWithDeviceType).toHaveBeenCalledWith([1], mockSoftwareBuilds, [deviceTypesEnum.CLIENT_DEVICE, deviceTypesEnum.DEMO_DEVICE], SoftwareBuildListResource);
    });

    it('should call changeInventoryStatus for Mark as active operation', async () => {
      spyOn(service, 'changeInventoryStatus').and.returnValue(Promise.resolve(true));
      spyOn(service as any, 'showSuccessAndRefresh');

      service.changeOperationForSoftwareBuild('Mark as active', SoftwareBuildListResource, [1], mockSoftwareBuilds);

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(service.changeInventoryStatus).toHaveBeenCalledWith([1], mockSoftwareBuilds, true, SoftwareBuildListResource);
    });

    it('should call changeInventoryStatus for Mark as Inactive operation', async () => {
      spyOn(service, 'changeInventoryStatus').and.returnValue(Promise.resolve(true));
      spyOn(service as any, 'showSuccessAndRefresh');

      service.changeOperationForSoftwareBuild('Mark as Inactive', SoftwareBuildListResource, [1], mockSoftwareBuilds);

      await new Promise(resolve => setTimeout(resolve, 0));

      expect(service.changeInventoryStatus).toHaveBeenCalledWith([1], mockSoftwareBuilds, false, SoftwareBuildListResource);
    });

    it('should handle default case for unknown operation', () => {
      service.changeOperationForSoftwareBuild('Unknown Operation', SoftwareBuildListResource, [1], mockSoftwareBuilds);

      // Should not call any operation methods
      expect(toastrServiceSpy.info).not.toHaveBeenCalled();
    });
  });

  // ==================== MAP WITH DEVICE TYPE TESTS ====================

  describe('mapWithDeviceType', () => {
    const mockSoftwareBuilds: SoftwareBuildListResponse[] = [
      { id: 1, version: 'v1.0', countries: ['USA'] } as SoftwareBuildListResponse
    ];

    it('should map with device type successfully', async () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: { message: 'Mapped successfully' }
      });

      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(true);
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(true));
      softwareBuildApiCallServiceSpy.mapInventoryWithDeviceType.and.returnValue(of(mockResponse as any));

      const result = await service.mapWithDeviceType([1], mockSoftwareBuilds, [deviceTypesEnum.CLIENT_DEVICE], SoftwareBuildListResource);

      expect(result).toBe(true);
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Mapped successfully');
    });

    it('should return false when validation fails', async () => {
      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(false);

      const result = await service.mapWithDeviceType([1], mockSoftwareBuilds, [deviceTypesEnum.CLIENT_DEVICE], SoftwareBuildListResource);

      expect(result).toBe(false);
    });

    it('should return false when no permission', async () => {
      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(true);
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(false);

      const result = await service.mapWithDeviceType([1], mockSoftwareBuilds, [deviceTypesEnum.CLIENT_DEVICE], SoftwareBuildListResource);

      expect(result).toBe(false);
      expect(toastrServiceSpy.error).toHaveBeenCalledWith(PERMISSION_ERROR_MESSAGE);
    });

    it('should return false when user cancels confirmation', async () => {
      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(true);
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(false));

      const result = await service.mapWithDeviceType([1], mockSoftwareBuilds, [deviceTypesEnum.CLIENT_DEVICE], SoftwareBuildListResource);

      expect(result).toBe(false);
    });

    it('should handle API error', async () => {
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Internal Server Error' });

      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(true);
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(true));
      softwareBuildApiCallServiceSpy.mapInventoryWithDeviceType.and.returnValue(throwError(() => mockError));

      const result = await service.mapWithDeviceType([1], mockSoftwareBuilds, [deviceTypesEnum.CLIENT_DEVICE], SoftwareBuildListResource);

      expect(result).toBe(false);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(mockError);
    });
  });

  // ==================== CHANGE INVENTORY STATUS TESTS ====================

  describe('changeInventoryStatus', () => {
    const mockSoftwareBuilds: SoftwareBuildListResponse[] = [
      { id: 1, version: 'v1.0', countries: ['USA'] } as SoftwareBuildListResponse
    ];

    it('should change inventory status to active successfully', async () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: { message: 'Status changed successfully' }
      });

      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(true);
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(true));
      softwareBuildApiCallServiceSpy.markInventoriesActiveInactive.and.returnValue(of(mockResponse as any));

      const result = await service.changeInventoryStatus([1], mockSoftwareBuilds, true, SoftwareBuildListResource);

      expect(result).toBe(true);
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Status changed successfully');
      expect(softWareBuildConformationServiceSpy.openInventoryOperationModel).toHaveBeenCalledWith(
        'Confirm', 'Cancel', 'Confirmation', 'Are you sure you want to mark Software Build(s) as Active?'
      );
    });

    it('should change inventory status to inactive successfully', async () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: { message: 'Status changed successfully' }
      });

      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(true);
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(true));
      softwareBuildApiCallServiceSpy.markInventoriesActiveInactive.and.returnValue(of(mockResponse as any));

      const result = await service.changeInventoryStatus([1], mockSoftwareBuilds, false, SoftwareBuildListResource);

      expect(result).toBe(true);
      expect(softWareBuildConformationServiceSpy.openInventoryOperationModel).toHaveBeenCalledWith(
        'Confirm', 'Cancel', 'Confirmation', 'Are you sure you want to mark Software Build(s) as Inactive? Doing same will remove the release association with test device if any.'
      );
    });

    it('should return false when validation fails', async () => {
      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(false);

      const result = await service.changeInventoryStatus([1], mockSoftwareBuilds, true, SoftwareBuildListResource);

      expect(result).toBe(false);
    });

    it('should return false when no permission', async () => {
      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(true);
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(false);

      const result = await service.changeInventoryStatus([1], mockSoftwareBuilds, true, SoftwareBuildListResource);

      expect(result).toBe(false);
      expect(toastrServiceSpy.error).toHaveBeenCalledWith(PERMISSION_ERROR_MESSAGE);
    });

    it('should return false when user cancels confirmation', async () => {
      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(true);
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(false));

      const result = await service.changeInventoryStatus([1], mockSoftwareBuilds, true, SoftwareBuildListResource);

      expect(result).toBe(false);
    });

    it('should handle API error', async () => {
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Internal Server Error' });

      spyOn(service as any, 'validateSoftwareBuildPermissions').and.returnValue(true);
      permissionServiceSpy.getSoftwearBuildPermission.and.returnValue(true);
      softWareBuildConformationServiceSpy.openInventoryOperationModel.and.returnValue(Promise.resolve(true));
      softwareBuildApiCallServiceSpy.markInventoriesActiveInactive.and.returnValue(throwError(() => mockError));

      const result = await service.changeInventoryStatus([1], mockSoftwareBuilds, true, SoftwareBuildListResource);

      expect(result).toBe(false);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(mockError);
    });
  });

  // ==================== VALIDATION TESTS ====================

  describe('validateSoftwareBuildPermissions', () => {
    const mockSoftwareBuilds: SoftwareBuildListResponse[] = [
      { id: 1, version: 'v1.0', countries: ['USA'] } as SoftwareBuildListResponse
    ];

    it('should return false when no software builds selected', () => {
      const result = (service as any).validateSoftwareBuildPermissions([], [], SoftwareBuildListResource);

      expect(result).toBe(false);
      expect(toastrServiceSpy.info).toHaveBeenCalledWith('Please Select Software Build(s)');
    });

    it('should return true when validation passes', () => {
      moduleValidationServiceSpy.validateWithUserCountryForMultileRecord.and.returnValue(true);

      const result = (service as any).validateSoftwareBuildPermissions([1], mockSoftwareBuilds, SoftwareBuildListResource);

      expect(result).toBe(true);
      expect(moduleValidationServiceSpy.validateWithUserCountryForMultileRecord).toHaveBeenCalledWith(['USA'], SoftwareBuildListResource, true);
    });

    it('should return false when country validation fails', () => {
      moduleValidationServiceSpy.validateWithUserCountryForMultileRecord.and.returnValue(false);

      const result = (service as any).validateSoftwareBuildPermissions([1], mockSoftwareBuilds, SoftwareBuildListResource);

      expect(result).toBe(false);
    });
  });

  describe('getItemAssociatedCountryList', () => {
    it('should return country list from software builds', () => {
      const mockSoftwareBuilds: SoftwareBuildListResponse[] = [
        { id: 1, version: 'v1.0', countries: ['USA', 'Canada'] } as SoftwareBuildListResponse,
        { id: 2, version: 'v2.0', countries: ['UK'] } as SoftwareBuildListResponse
      ];

      const result = (service as any).getItemAssociatedCountryList(mockSoftwareBuilds);

      expect(result).toEqual(['UK']); // Returns the last non-empty countries array
    });

    it('should return empty array when no countries', () => {
      const mockSoftwareBuilds: SoftwareBuildListResponse[] = [
        { id: 1, version: 'v1.0', countries: [] } as SoftwareBuildListResponse
      ];

      const result = (service as any).getItemAssociatedCountryList(mockSoftwareBuilds);

      expect(result).toEqual([]);
    });
  });

  // ==================== DOWNLOAD JSON FILE TESTS ====================

  describe('downloadJSONFile', () => {
    it('should download JSON file successfully', async () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: {
          data: { version: '1.0', content: 'test data' }
        } as any
      });

      videoServiceSpy.downloadJSONFile.and.returnValue(of(mockResponse as any));

      // Mock DOM methods
      const mockLink = {
        href: '',
        setAttribute: jasmine.createSpy('setAttribute'),
        click: jasmine.createSpy('click')
      };
      spyOn(document, 'createElement').and.returnValue(mockLink as any);
      spyOn(document.body, 'appendChild');
      spyOn(document.body, 'removeChild');
      spyOn(window.URL, 'createObjectURL').and.returnValue('blob:url');

      await service.downloadJSONFile(1, 'test-version', SoftwareBuildListResource);

      expect(videoServiceSpy.downloadJSONFile).toHaveBeenCalledWith(1);
      expect(document.createElement).toHaveBeenCalledWith('a');
      expect(mockLink.setAttribute).toHaveBeenCalledWith('download', 'test-version.json');
      expect(mockLink.click).toHaveBeenCalled();
    });

    it('should handle download JSON file error', async () => {
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Internal Server Error' });

      videoServiceSpy.downloadJSONFile.and.returnValue(throwError(() => mockError));

      await service.downloadJSONFile(1, 'test-version', SoftwareBuildListResource);

      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(mockError);
    });

    it('should handle response without data', async () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: {} as any
      });

      videoServiceSpy.downloadJSONFile.and.returnValue(of(mockResponse as any));

      await service.downloadJSONFile(1, 'test-version', SoftwareBuildListResource);

      expect(videoServiceSpy.downloadJSONFile).toHaveBeenCalledWith(1);
      // Should complete without error
    });
  });

  // ==================== PRIVATE METHOD TESTS ====================

  describe('showSuccessAndRefresh', () => {
    it('should call refresh page subject', () => {
      spyOn(service, 'callRefreshPageSubject');

      (service as any).showSuccessAndRefresh(SoftwareBuildListResource);

      expect(service.callRefreshPageSubject).toHaveBeenCalledWith(
        jasmine.any(ListingPageReloadSubjectParameter),
        SoftwareBuildListResource,
        false,
        null
      );
    });
  });

  describe('getEmptySoftwareBuildListResult', () => {
    it('should return empty result object', () => {
      const result = (service as any).getEmptySoftwareBuildListResult();

      expect(result).toEqual({
        success: false,
        softwareBuilds: [],
        totalSoftwareBuildDisplay: 0,
        totalSoftwareBuilds: 0,
        localSoftwareBuildList: [],
        totalItems: 0,
        page: 0
      });
    });
  });

  describe('processSoftwareBuildDates', () => {
    it('should process dates correctly', () => {
      const mockSoftwareBuilds = [
        { id: 1, version: 'v1.0', createdDate: 1640995200000 },
        { id: 2, version: 'v2.0', createdDate: null }
      ];

      datePipeSpy.transform.and.returnValue('Jan 1, 2022, 12:00:00 AM');

      const result = (service as any).processSoftwareBuildDates(mockSoftwareBuilds);

      expect(result[0].createdDate).toBe('Jan 1, 2022, 12:00:00 AM');
      expect(result[1].createdDate).toBeNull();
    });
  });
});
