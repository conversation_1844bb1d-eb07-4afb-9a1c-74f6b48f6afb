import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DeviceConnectionHistoryFilterComponent } from './device-connection-history-filter.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { DeviceConnectionHistoryOperationService } from '../Device-Connection-History-Service/Device-Connection-History-Operation/device-connection-history-operation-service.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { Subject } from 'rxjs';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistorySearchRequestBody.model';

import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';

describe('DeviceConnectionHistoryFilterComponent', () => {
  let component: DeviceConnectionHistoryFilterComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryFilterComponent>;

  // Service mocks
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let multiSelectDropDownSettingServiceSpy: jasmine.SpyObj<MultiSelectDropDownSettingService>;
  let deviceConnectionHistoryServiceSpy: jasmine.SpyObj<DeviceConnectionHistoryOperationService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let validationServiceSpy: jasmine.SpyObj<ValidationService>;
  let keyValueMappingServiceSpy: jasmine.SpyObj<KeyValueMappingServiceService>;

  // Mock data
  const mockOsTypeList: EnumMapping[] = [
    { key: 'WINDOWS', value: 'Windows' },
    { key: 'LINUX', value: 'Linux' },
    { key: 'MAC', value: 'Mac' }
  ];

  const mockDropdownSettings: MultiSelectDropdownSettings = {
    singleSelection: false,
    idField: 'key',
    textField: 'value',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 3,
    allowSearchFilter: true,
    noDataAvailablePlaceholderText: 'No data available'
  };

  const mockSearchRequestBody = new DeviceConnectionHistorySearchRequestBody(
    'TestManufacturer',
    'TestModel',
    'TEST123',
    null,
    1672531200000
  );

  beforeEach(async () => {
    // Create service spies
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    multiSelectDropDownSettingServiceSpy = jasmine.createSpyObj('MultiSelectDropDownSettingService', ['getOTSTypeDrpSetting']);
    deviceConnectionHistoryServiceSpy = jasmine.createSpyObj('DeviceConnectionHistoryOperationService', [
      'getDeviceConnectionHistoryListRefreshSubject',
      'processFilterSearch',
      'buildDeviceConnectionHistoryFilterRequestBody',
      'setLastAppliedDeviceConnectionHistorySearchRequest',
      'getLastAppliedDeviceConnectionHistorySearchRequest',
      'clearAllFiltersAndRefresh'
    ]);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['getEnumMappingSelectedValue']);
    validationServiceSpy = jasmine.createSpyObj('ValidationService', ['removeSpaces']);
    keyValueMappingServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', ['enumOptionToList']);

    // Set up default return values
    multiSelectDropDownSettingServiceSpy.getOTSTypeDrpSetting.and.returnValue(mockDropdownSettings);
    keyValueMappingServiceSpy.enumOptionToList.and.returnValue(mockOsTypeList);
    commonsServiceSpy.getEnumMappingSelectedValue.and.returnValue([{ key: 'WINDOWS', value: 'Windows' }]);
    deviceConnectionHistoryServiceSpy.processFilterSearch.and.returnValue(true);
    deviceConnectionHistoryServiceSpy.buildDeviceConnectionHistoryFilterRequestBody.and.returnValue(mockSearchRequestBody);
    deviceConnectionHistoryServiceSpy.getLastAppliedDeviceConnectionHistorySearchRequest.and.returnValue(null);

    // Create mock subject
    const mockRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();
    deviceConnectionHistoryServiceSpy.getDeviceConnectionHistoryListRefreshSubject.and.returnValue(mockRefreshSubject);

    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryFilterComponent],
      imports: [
        MatFormFieldModule,
        MatDatepickerModule,
        BrowserAnimationsModule,
        MatNativeDateModule,
        MatInputModule,
        ReactiveFormsModule,
        FormsModule,
        NgMultiSelectDropDownModule.forRoot()
      ],
      providers: [
        { provide: MultiSelectDropDownSettingService, useValue: multiSelectDropDownSettingServiceSpy },
        { provide: DeviceConnectionHistoryOperationService, useValue: deviceConnectionHistoryServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: ValidationService, useValue: validationServiceSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingServiceSpy },
        LocalStorageService,
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        PrintListPipe,
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryFilterComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ==================== INITIALIZATION TESTS ====================
  describe('Component Initialization', () => {
    it('should initialize with API call when isFilterComponentInitWithApicall is true', () => {
      component.isFilterComponentInitWithApicall = true;
      spyOn(component, 'clearFilter');
      spyOn(component as any, 'onInitSubject');
      spyOn(component as any, 'getFilterList');

      component.ngOnInit();

      expect(multiSelectDropDownSettingServiceSpy.getOTSTypeDrpSetting).toHaveBeenCalled();
      expect(component.dropdownSettingsForOsType).toEqual(mockDropdownSettings);
      expect((component as any).onInitSubject).toHaveBeenCalled();
      expect((component as any).getFilterList).toHaveBeenCalled();
      expect(component.clearFilter).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });

    it('should restore cached filter data when isFilterComponentInitWithApicall is false', () => {
      component.isFilterComponentInitWithApicall = false;
      spyOn(component as any, 'restoreCachedFilterData');
      spyOn(component as any, 'onInitSubject');
      spyOn(component as any, 'getFilterList');

      component.ngOnInit();

      expect((component as any).restoreCachedFilterData).toHaveBeenCalled();
    });

    it('should initialize dropdown settings and filter list', async () => {
      spyOn(component as any, 'setFilterValue');

      await (component as any).getFilterList();

      expect(keyValueMappingServiceSpy.enumOptionToList).toHaveBeenCalled();
      expect(component.osTypeList).toEqual(mockOsTypeList);
      expect((component as any).setFilterValue).toHaveBeenCalled();
    });

    it('should set default values correctly', () => {
      expect(component.textBoxMaxLengthMessage).toBeDefined();
      expect(component.specialCharacterErrorMessage).toBeDefined();
      expect(component.maxdate).toBeInstanceOf(Date);
      expect(component.serialNumberOrHwId).toBeDefined();
      expect(component.deviceModel).toBeDefined();
      expect(component.manufacturer).toBeDefined();
      expect(component.osType).toBeDefined();
      expect(component.lastConnectedDate).toBeDefined();
      expect(component.searchBtnText).toBeDefined();
      expect(component.clearBtnText).toBeDefined();
      expect(component.filterDeviceConnectionHistoryForm).toBeDefined();
      expect(component.defaultListingPageReloadSubjectParameter).toBeDefined();
    });
  });

  // ==================== FORM VALIDATION TESTS ====================
  describe('Form Validation', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should initialize form with correct validators', () => {
      const form = component.filterDeviceConnectionHistoryForm;

      expect(form.get('serialNumber')).toBeDefined();
      expect(form.get('deviceModel')).toBeDefined();
      expect(form.get('manufacturer')).toBeDefined();
      expect(form.get('osType')).toBeDefined();
      expect(form.get('lastConnectedDateAndTime')).toBeDefined();

      // Test initial values
      expect(form.get('serialNumber')?.value).toBeNull();
      expect(form.get('deviceModel')?.value).toBeNull();
      expect(form.get('manufacturer')?.value).toBeNull();
      expect(form.get('osType')?.value).toEqual([]);
      expect(form.get('lastConnectedDateAndTime')?.value).toBeNull();
    });

    it('should validate form fields correctly', () => {
      const form = component.filterDeviceConnectionHistoryForm;

      // Test max length validation
      form.get('serialNumber')?.setValue('a'.repeat(256)); // Assuming max length is 255
      expect(form.get('serialNumber')?.invalid).toBe(true);

      form.get('serialNumber')?.setValue('validSerial');
      expect(form.get('serialNumber')?.valid).toBe(true);
    });

    it('should handle form reset correctly', () => {
      const form = component.filterDeviceConnectionHistoryForm;

      // Set some values
      form.get('serialNumber')?.setValue('test');
      form.get('deviceModel')?.setValue('testModel');

      form.reset();

      expect(form.get('serialNumber')?.value).toBeNull();
      expect(form.get('deviceModel')?.value).toBeNull();
    });
  });

  // ==================== SUBSCRIPTION TESTS ====================
  describe('Subject Subscriptions', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should handle refresh list subscription with clear filter', () => {
      spyOn(component, 'clearFilter');

      (component as any).onInitSubject();

      const refreshSubject = deviceConnectionHistoryServiceSpy.getDeviceConnectionHistoryListRefreshSubject();
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, true, false);

      refreshSubject.next(mockParameter);

      expect(component.clearFilter).toHaveBeenCalledWith(mockParameter);
    });

    it('should handle refresh list subscription without clear filter', () => {
      spyOn(component as any, 'deviceConnectionHistoryListPageRefresh');

      (component as any).onInitSubject();

      const refreshSubject = deviceConnectionHistoryServiceSpy.getDeviceConnectionHistoryListRefreshSubject();
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      refreshSubject.next(mockParameter);

      expect((component as any).deviceConnectionHistoryListPageRefresh).toHaveBeenCalledWith(mockParameter);
    });

    it('should not process when isReloadData is false', () => {
      spyOn(component, 'clearFilter');
      spyOn(component as any, 'deviceConnectionHistoryListPageRefresh');

      (component as any).onInitSubject();

      const refreshSubject = deviceConnectionHistoryServiceSpy.getDeviceConnectionHistoryListRefreshSubject();
      const mockParameter = new ListingPageReloadSubjectParameter(false, true, false, false);

      refreshSubject.next(mockParameter);

      expect(component.clearFilter).not.toHaveBeenCalled();
      expect((component as any).deviceConnectionHistoryListPageRefresh).not.toHaveBeenCalled();
    });
  });

  // ==================== LIFECYCLE TESTS ====================
  describe('Component Lifecycle', () => {
    it('should unsubscribe from refresh subscription on destroy', () => {
      fixture.detectChanges();
      (component as any).onInitSubject();

      // Create a spy on the subscription
      const subscriptionSpy = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component['subscriptionForRefeshList'] = subscriptionSpy;

      component.ngOnDestroy();

      expect(subscriptionSpy.unsubscribe).toHaveBeenCalled();
    });

    it('should handle undefined subscription on destroy', () => {
      component['subscriptionForRefeshList'] = undefined;

      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  // ==================== FILTER VALUE SETTING TESTS ====================
  describe('Filter Value Setting', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should set filter values from search request body', () => {
      component.deviceConnectionHistorySearchRequestBody = mockSearchRequestBody;
      commonsServiceSpy.getEnumMappingSelectedValue.and.returnValue([{ key: 'WINDOWS', value: 'Windows' }]);

      (component as any).setFilterValue();

      expect(component.filterDeviceConnectionHistoryForm.get('serialNumber')?.value).toBe('TEST123');
      expect(component.filterDeviceConnectionHistoryForm.get('deviceModel')?.value).toBe('TestModel');
      expect(component.filterDeviceConnectionHistoryForm.get('manufacturer')?.value).toBe('TestManufacturer');
      expect(component.filterDeviceConnectionHistoryForm.get('lastConnectedDateAndTime')?.value).toBe(1672531200000);
    });

    it('should handle null search request body', () => {
      component.deviceConnectionHistorySearchRequestBody = null;

      (component as any).setFilterValue();

      expect(component.filterDeviceConnectionHistoryForm.get('serialNumber')?.value).toBeNull();
      expect(component.filterDeviceConnectionHistoryForm.get('deviceModel')?.value).toBeNull();
      expect(component.filterDeviceConnectionHistoryForm.get('manufacturer')?.value).toBeNull();
    });

    it('should call deviceConnectionHistoryListPageRefresh when listPageRefreshForbackToDetailPage is true', () => {
      component.listPageRefreshForbackToDetailPage = true;
      spyOn(component as any, 'deviceConnectionHistoryListPageRefresh');

      (component as any).setFilterValue();

      expect((component as any).deviceConnectionHistoryListPageRefresh).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });

    it('should restore cached filter data', () => {
      const cachedData = mockSearchRequestBody;
      deviceConnectionHistoryServiceSpy.getLastAppliedDeviceConnectionHistorySearchRequest.and.returnValue(cachedData);
      spyOn(component as any, 'setFilterValue');

      (component as any).restoreCachedFilterData();

      expect(deviceConnectionHistoryServiceSpy.getLastAppliedDeviceConnectionHistorySearchRequest).toHaveBeenCalled();
      expect(component.deviceConnectionHistorySearchRequestBody).toEqual(cachedData);
      expect((component as any).setFilterValue).toHaveBeenCalled();
    });

    it('should handle null cached filter data', () => {
      deviceConnectionHistoryServiceSpy.getLastAppliedDeviceConnectionHistorySearchRequest.and.returnValue(null);
      spyOn(component as any, 'setFilterValue');

      (component as any).restoreCachedFilterData();

      expect((component as any).setFilterValue).not.toHaveBeenCalled();
    });
  });

  // ==================== SEARCH FUNCTIONALITY TESTS ====================
  describe('Search Functionality', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should search data successfully', () => {
      const formValue = {
        serialNumber: 'TEST123',
        deviceModel: 'TestModel',
        manufacturer: 'TestManufacturer',
        osType: [{ key: 'WINDOWS', value: 'Windows' }],
        lastConnectedDateAndTime: new Date()
      };

      component.filterDeviceConnectionHistoryForm.patchValue(formValue);
      deviceConnectionHistoryServiceSpy.processFilterSearch.and.returnValue(true);

      component.searchData();

      expect(deviceConnectionHistoryServiceSpy.processFilterSearch).toHaveBeenCalledWith(
        formValue,
        component.filterDeviceConnectionHistoryForm.invalid,
        component.defaultListingPageReloadSubjectParameter
      );
      expect(deviceConnectionHistoryServiceSpy.buildDeviceConnectionHistoryFilterRequestBody).toHaveBeenCalledWith(formValue);
      expect(deviceConnectionHistoryServiceSpy.setLastAppliedDeviceConnectionHistorySearchRequest).toHaveBeenCalledWith(mockSearchRequestBody);
    });

    it('should not proceed when validation fails', () => {
      deviceConnectionHistoryServiceSpy.processFilterSearch.and.returnValue(false);

      component.searchData();

      expect(deviceConnectionHistoryServiceSpy.buildDeviceConnectionHistoryFilterRequestBody).not.toHaveBeenCalled();
      expect(deviceConnectionHistoryServiceSpy.setLastAppliedDeviceConnectionHistorySearchRequest).not.toHaveBeenCalled();
    });

    it('should call searchData when searchFilteredDeviceConnectionHistory is called', () => {
      spyOn(component, 'searchData');

      component.searchFilteredDeviceConnectionHistory();

      expect(component.searchData).toHaveBeenCalled();
    });
  });

  // ==================== CLEAR FILTER TESTS ====================
  describe('Clear Filter Functionality', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should clear filter with default parameter', () => {
      spyOn(component as any, 'clearAllFilter');

      component.clearFilter();

      expect((component as any).clearAllFilter).toHaveBeenCalled();
      expect(deviceConnectionHistoryServiceSpy.setLastAppliedDeviceConnectionHistorySearchRequest).toHaveBeenCalledWith(null);
      expect(deviceConnectionHistoryServiceSpy.clearAllFiltersAndRefresh).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });

    it('should clear filter with provided parameter', () => {
      const customParameter = new ListingPageReloadSubjectParameter(true, false, true, false);
      spyOn(component as any, 'clearAllFilter');

      component.clearFilter(customParameter);

      expect((component as any).clearAllFilter).toHaveBeenCalled();
      expect(deviceConnectionHistoryServiceSpy.setLastAppliedDeviceConnectionHistorySearchRequest).toHaveBeenCalledWith(null);
      expect(deviceConnectionHistoryServiceSpy.clearAllFiltersAndRefresh).toHaveBeenCalledWith(customParameter);
    });

    it('should clear all filter form values', () => {
      // Set some values first
      component.filterDeviceConnectionHistoryForm.patchValue({
        serialNumber: 'test',
        deviceModel: 'testModel',
        manufacturer: 'testManufacturer',
        osType: [{ key: 'WINDOWS', value: 'Windows' }],
        lastConnectedDateAndTime: new Date()
      });

      (component as any).clearAllFilter();

      expect(component.filterDeviceConnectionHistoryForm.get('serialNumber')?.value).toBeNull();
      expect(component.filterDeviceConnectionHistoryForm.get('deviceModel')?.value).toBeNull();
      expect(component.filterDeviceConnectionHistoryForm.get('manufacturer')?.value).toBeNull();
      expect(component.filterDeviceConnectionHistoryForm.get('osType')?.value).toEqual([]);
      expect(component.filterDeviceConnectionHistoryForm.get('lastConnectedDateAndTime')?.value).toBeNull();
    });
  });

  // ==================== PAGE REFRESH TESTS ====================
  describe('Page Refresh Functionality', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should refresh page with valid form', () => {
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const formValue = {
        serialNumber: 'TEST123',
        deviceModel: 'TestModel',
        manufacturer: 'TestManufacturer',
        osType: [],
        lastConnectedDateAndTime: null
      };

      component.filterDeviceConnectionHistoryForm.patchValue(formValue);

      (component as any).deviceConnectionHistoryListPageRefresh(mockParameter);

      expect(deviceConnectionHistoryServiceSpy.buildDeviceConnectionHistoryFilterRequestBody).toHaveBeenCalledWith(formValue);
      expect(deviceConnectionHistoryServiceSpy.setLastAppliedDeviceConnectionHistorySearchRequest).toHaveBeenCalledWith(mockSearchRequestBody);
    });

    it('should reset form when invalid', () => {
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      // Make form invalid
      component.filterDeviceConnectionHistoryForm.setErrors({ invalid: true });
      spyOn(component.filterDeviceConnectionHistoryForm, 'reset');

      (component as any).deviceConnectionHistoryListPageRefresh(mockParameter);

      expect(component.filterDeviceConnectionHistoryForm.reset).toHaveBeenCalled();
    });
  });

  // ==================== CONSTANTS AND PROPERTIES TESTS ====================
  describe('Constants and Properties', () => {
    it('should have correct constant values', () => {
      expect(component.textBoxMaxLengthMessage).toBeDefined();
      expect(component.specialCharacterErrorMessage).toBeDefined();
      expect(component.serialNumberOrHwId).toBeDefined();
      expect(component.deviceModel).toBeDefined();
      expect(component.manufacturer).toBeDefined();
      expect(component.osType).toBeDefined();
      expect(component.lastConnectedDate).toBeDefined();
      expect(component.searchBtnText).toBeDefined();
      expect(component.clearBtnText).toBeDefined();
    });

    it('should initialize with correct default values', () => {
      expect(component.maxdate).toBeInstanceOf(Date);
      expect(component.filterDeviceConnectionHistoryForm).toBeDefined();
      expect(component.defaultListingPageReloadSubjectParameter).toBeDefined();
      expect(component.dropdownSettingsForOsType).toBeNull();
      expect(component.osTypeList).toEqual([]);
    });
  });

  // ==================== ERROR HANDLING TESTS ====================
  describe('Error Handling', () => {
    beforeEach(() => {
      fixture.detectChanges();
    });

    it('should handle form validation errors gracefully', () => {
      // Set invalid values
      component.filterDeviceConnectionHistoryForm.patchValue({
        serialNumber: 'a'.repeat(300), // Assuming this exceeds max length
        deviceModel: 'a'.repeat(300),
        manufacturer: 'a'.repeat(300)
      });

      expect(component.filterDeviceConnectionHistoryForm.invalid).toBe(true);
    });

    it('should handle empty form submission', () => {
      deviceConnectionHistoryServiceSpy.processFilterSearch.and.returnValue(false);

      component.searchData();

      expect(deviceConnectionHistoryServiceSpy.processFilterSearch).toHaveBeenCalled();
      expect(deviceConnectionHistoryServiceSpy.buildDeviceConnectionHistoryFilterRequestBody).not.toHaveBeenCalled();
    });

    it('should handle service errors gracefully', () => {
      deviceConnectionHistoryServiceSpy.processFilterSearch.and.throwError('Service error');

      expect(() => component.searchData()).not.toThrow();
    });
  });
});
