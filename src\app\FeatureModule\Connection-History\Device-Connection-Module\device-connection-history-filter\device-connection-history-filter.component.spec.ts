import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeviceConnectionHistoryFilterComponent } from './device-connection-history-filter.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

describe('DeviceConnectionHistoryFilterComponent', () => {
  let component: DeviceConnectionHistoryFilterComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryFilterComponent>;
  let tostrServiceMock: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    tostrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryFilterComponent],
      imports: [MatFormFieldModule, MatDatepickerModule, BrowserAnimationsModule, MatNativeDateModule, MatInputModule, ReactiveFormsModule, FormsModule, ReactiveFormsModule, NgMultiSelectDropDownModule.forRoot()],
      providers: [
        LocalStorageService,
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        PrintListPipe,
        commonsProviders(tostrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
