import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SoftwareBuildModuleComponent } from './software-build-module.component';
import { SoftwareBuildOperationsService } from '../software-build-services/software-build-operations.service';
import { UploadScanService } from 'src/app/shared/upload-scan.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { DatePipe, CommonModule } from '@angular/common';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { ConfirmDialogService } from '../../CommonComponent/confirmationdialog/confirmation.service';

describe('SoftwareBuildModuleComponent', () => {
  let component: SoftwareBuildModuleComponent;
  let fixture: ComponentFixture<SoftwareBuildModuleComponent>;

  let localStorageSpy: jasmine.SpyObj<LocalStorageService>;
  let sessionStorageSpy: jasmine.SpyObj<SessionStorageService>;

  beforeEach(async () => {
    localStorageSpy = jasmine.createSpyObj('LocalStorageService', ['retrieve', 'store', 'clear']);
    sessionStorageSpy = jasmine.createSpyObj('SessionStorageService', ['retrieve', 'store', 'clear']);

    // Optional: set return values for specific calls
    localStorageSpy.retrieve.and.returnValue(null);
    sessionStorageSpy.retrieve.and.returnValue(null);

    await TestBed.configureTestingModule({
      declarations: [
        SoftwareBuildModuleComponent
      ],
      imports: [CommonModule, NgbPaginationModule, ReactiveFormsModule, FormsModule, NgMultiSelectDropDownModule.forRoot()],
      providers: [
        UploadScanService,
        PermissionService,
        AuthJwtService,
        ConfirmDialogService,
        SoftwareBuildOperationsService,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        DatePipe,
        { provide: LocalStorageService, useValue: localStorageSpy },
        { provide: SessionStorageService, useValue: sessionStorageSpy },
        commonsProviders(null)
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildModuleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ==================== COMPONENT INITIALIZATION TESTS ====================

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.isSoftwareBuildListingPageDisplay).toBe(true);
      expect(component.isFilterComponentInitWithApicall).toBe(true);
    });

    it('should call setDefaultState on ngOnInit', () => {
      spyOn(component as any, 'setDefaultState');

      component.ngOnInit();

      expect((component as any).setDefaultState).toHaveBeenCalled();
    });

    it('should call setDefaultState on ngOnDestroy', () => {
      spyOn(component as any, 'setDefaultState');

      component.ngOnDestroy();

      expect((component as any).setDefaultState).toHaveBeenCalled();
    });

    it('should set default state correctly', () => {
      const softwareBuildOperationsService = TestBed.inject(SoftwareBuildOperationsService);
      spyOn(softwareBuildOperationsService, 'setSoftwareBuildSearchRequestBodyForListingApi');
      spyOn(softwareBuildOperationsService, 'setIsFilterHiddenForListing');
      spyOn(softwareBuildOperationsService, 'setListPageRefreshForbackToOtherPage');

      (component as any).setDefaultState();

      expect(softwareBuildOperationsService.setSoftwareBuildSearchRequestBodyForListingApi).toHaveBeenCalledWith(null);
      expect(softwareBuildOperationsService.setIsFilterHiddenForListing).toHaveBeenCalledWith(false);
      expect(softwareBuildOperationsService.setListPageRefreshForbackToOtherPage).toHaveBeenCalledWith(false);
    });
  });

});
