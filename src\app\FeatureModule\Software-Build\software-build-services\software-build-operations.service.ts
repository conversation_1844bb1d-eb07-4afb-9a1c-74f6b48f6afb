import { Injectable } from '@angular/core';
import { Subject, firstValueFrom } from 'rxjs';
import { HttpErrorResponse } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';
import { isNullOrUndefined } from 'is-what';
import { PERMISSION_ERROR_MESSAGE, SoftwareBuildListResource, COMMON_SELECT_FILTER } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { SoftwareBuildFilterAction } from 'src/app/model/SoftwaarBuilds/SoftwareFilterAction.model';
import { SoftwareBuildListResponse } from 'src/app/model/SoftwaarBuilds/SoftwareBuildListResponse.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { SoftwareBuildApiCallService } from './software-api-call/software-build-api-call.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';
import { SoftWareBuildConformationService } from './conformation-software-build-dialog/software-build-conformation.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { DatePipe } from '@angular/common';
import { VideoService } from 'src/app/shared/videoservice/video.service';
import { SoftwareBuildPageResponse } from 'src/app/model/SoftwaarBuilds/Interface/SoftwareBuildPageResponse.model';
import { SoftwareBuildListResult } from 'src/app/model/SoftwaarBuilds/Interface/SoftwareBuildListResult.model';
import { SoftwareBuildOperations } from 'src/app/shared/enum/Operations/SoftwareBuildOperations.enum';

/**
* Software Build Operations Service for centralized operation management
* Follows the same pattern as probe operations service
*
* <AUTHOR>
*/
@Injectable({
  providedIn: 'root'
})
export class SoftwareBuildOperationsService {

  constructor(
    private softwareBuildApiCallService: SoftwareBuildApiCallService,
    private permissionService: PermissionService,
    private exceptionHandlingService: ExceptionHandlingService,
    private moduleValidationService: ModuleValidationServiceService,
    private softWareBuildConformationService: SoftWareBuildConformationService,
    private commonsService: CommonsService,
    private toastrService: ToastrService,
    private datePipe: DatePipe,
    private videoService: VideoService
  ) { }

  //Loading Status
  private softwareBuildListLoadingSubject = new Subject<boolean>();

  //Refresh Software Build List
  private softwareBuildListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Software Build list filter
  private softwareBuildListFilterRequestParameterSubject = new Subject<SoftwareBuildFilterAction>();

  //Software Build Listing Page State Manage
  softwareBuildSearchRequestBody: SoftwareBuildSearchRequestBody = null;
  isFilterHidden: boolean = false;
  listPageRefreshForbackToOtherPage: boolean = false;

  private jsonVersionList: Array<Jsonlist> = [];
  private countryList: Array<CountryListResponse> = [];

  /**
  * Software Build List Page Loading
  * <AUTHOR>
  * @returns Subject<boolean>
  */
  public getSoftwareBuildListLoadingSubject(): Subject<boolean> {
    return this.softwareBuildListLoadingSubject;
  }

  public callSoftwareBuildListLoadingSubject(status: boolean): void {
    this.softwareBuildListLoadingSubject.next(status);
  }


  /**
  * This function call the subject for loading start and stop
  * <AUTHOR>
  * @param status
  * @param resourceName
  */
  public isLoading(status: boolean, resourceName: string): void {
    if (resourceName == SoftwareBuildListResource) {
      this.callSoftwareBuildListLoadingSubject(status);
    }
  }

  /**
  * Software Build List Page Refresh After some Action Like Create or Update or Delete Device
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getSoftwareBuildListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.softwareBuildListRefreshSubject;
  }

  public callSoftwareBuildListFilterRequestParameterSubject(softwareBuildFilterAction: SoftwareBuildFilterAction): void {
    this.softwareBuildListFilterRequestParameterSubject.next(softwareBuildFilterAction);
  }

  /**
   * Role List Page Refresh After some Action Like Serch parameter add
   * Note : Create or Update or Delete Role After Clear All filter and refresh page 
   * <AUTHOR>
   * @returns RoleList 
   */
  public getSoftwareBuildListFilterRequestParameterSubject(): Subject<SoftwareBuildFilterAction> {
    return this.softwareBuildListFilterRequestParameterSubject;
  }

  /**
  * This function call the subject for reload the page data
  * Note : (SoftwareBuildListResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter
  * @param resourceName
  * @param isFilterHidden
  * @param softwareBuildListFilterRequestBodyApply
  */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean, softwareBuildListFilterRequestBodyApply: SoftwareBuildSearchRequestBody): void {
    if (resourceName == SoftwareBuildListResource) {
      // Always use filter subject for list refresh (same pattern as probe service)
      let softwareBuildRequestBody = new SoftwareBuildSearchRequestBody("", [], null, null, null, null);
      if (!isNullOrUndefined(softwareBuildListFilterRequestBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
        softwareBuildRequestBody = softwareBuildListFilterRequestBodyApply;
      }
      let softwareBuildFilterAction = new SoftwareBuildFilterAction(listingPageReloadSubjectParameter, softwareBuildRequestBody);
      this.callSoftwareBuildListFilterRequestParameterSubject(softwareBuildFilterAction);
    }
  }

  /**
   * Clear all filters and refresh the listing
   * <AUTHOR>
   * @param listingPageReloadSubjectParameter - Reload parameters
   */
  public clearAllFiltersAndRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    const emptyFilterRequestBody = new SoftwareBuildSearchRequestBody(null, null, null, null, null, null);
    const probeFilterAction = new SoftwareBuildFilterAction(listingPageReloadSubjectParameter, emptyFilterRequestBody);
    this.callSoftwareBuildListFilterRequestParameterSubject(probeFilterAction);
  }

  /**
  * Set JsonVersionList
  */
  public setJsonVersionList(jsonVersionList: Array<Jsonlist>): void {
    this.jsonVersionList = jsonVersionList;
  }

  /**
  * Get JsonVersionList
  */
  public getJsonVersionList(): Array<Jsonlist> {
    return this.jsonVersionList;
  }

  /**
  * Set Country List
  * <AUTHOR>
  * @param countryList - Array of countries to cache
  */
  public setCountryList(countryList: CountryListResponse[]): void {
    this.countryList = countryList;
  }

  /**
  * Get Country List
  * <AUTHOR>
  * @returns Cached array of countries
  */
  public getCountryList(): CountryListResponse[] {
    return this.countryList;
  }

  // ==================== STATE MANAGEMENT METHODS ====================

  /**
  * Set Software Build List Search Request Body
  *
  * <AUTHOR>
  * @param softwareBuildSearchRequestBody
  */
  public setSoftwareBuildSearchRequestBodyForListingApi(softwareBuildSearchRequestBody: SoftwareBuildSearchRequestBody) {
    this.softwareBuildSearchRequestBody = softwareBuildSearchRequestBody;
  }

  /**
  * Get Software Build List Search Request Body
  *
  * <AUTHOR>
  * @returns
  */
  public getSoftwareBuildSearchRequestBodyForListingApi(): SoftwareBuildSearchRequestBody {
    return this.softwareBuildSearchRequestBody;
  }

  /**
  * Set Filter Hide/Show
  * <AUTHOR>
  * @param isFilterHidden
  */
  public setIsFilterHiddenForListing(isFilterHidden: boolean) {
    this.isFilterHidden = isFilterHidden;
  }

  /**
  * Get Filter Hide/Show
  * <AUTHOR>
  * @param isFilterHidden
  */
  public getIsFilterHiddenForListing(): boolean {
    return this.isFilterHidden;
  }

  /**
  * set listPageRefreshForbackToOtherPage
  * <AUTHOR>
  * @param listPageRefreshForbackToOtherPage
  */
  public setListPageRefreshForbackToOtherPage(listPageRefreshForbackToOtherPage: boolean) {
    this.listPageRefreshForbackToOtherPage = listPageRefreshForbackToOtherPage;
  }

  /**
  * get listPageRefreshForbackToOtherPage
  * <AUTHOR>
  * @returns
  */
  public getListPageRefreshForbackToOtherPage(): boolean {
    return this.listPageRefreshForbackToOtherPage;
  }

  /**
  * Load software build list with search parameters and pagination
  * Handles API call, response processing, and error handling
  * <AUTHOR>
  * @param softwareBuildSearchRequestBody - Search criteria for filtering software builds
  * @param pageObj - Pagination parameters (page, size)
  * @returns Promise with software build list result
  */
  public async loadSoftwareBuildList(softwareBuildSearchRequestBody: SoftwareBuildSearchRequestBody, pageObj: any): Promise<SoftwareBuildListResult> {
    try {
      if (this.permissionService.getSoftwearBuildPermission(PermissionAction.GET_SOFTWARE_BUILD_ACTION)) {
        const response = await firstValueFrom(this.softwareBuildApiCallService.inventoryList(softwareBuildSearchRequestBody, pageObj));

        if (response.status === 200 && response.body) {
          // Cast the response body to the correct type since API service has incorrect typing
          const softwareBuildData = response.body as any as SoftwareBuildPageResponse;

          // Process dates for display
          const processedContent = this.processSoftwareBuildDates(softwareBuildData.content);

          return {
            success: true,
            softwareBuilds: processedContent,
            totalSoftwareBuildDisplay: softwareBuildData.numberOfElements,
            totalSoftwareBuilds: softwareBuildData.totalElements,
            localSoftwareBuildList: processedContent,
            totalItems: softwareBuildData.totalElements,
            page: softwareBuildData.number + 1
          };
        } else {
          return this.getEmptySoftwareBuildListResult();
        }
      } else {
        this.toastrService.error('Insufficient permissions to load software build list');
        return this.getEmptySoftwareBuildListResult();
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return this.getEmptySoftwareBuildListResult();
    }
  }

  /**
  * Get empty software build list result for error cases
  * <AUTHOR>
  * @returns SoftwareBuildListResult
  */
  private getEmptySoftwareBuildListResult(): SoftwareBuildListResult {
    return {
      success: false,
      softwareBuilds: [],
      totalSoftwareBuildDisplay: 0,
      totalSoftwareBuilds: 0,
      localSoftwareBuildList: [],
      totalItems: 0,
      page: 0
    };
  }

  /**
  * Process software build dates for display
  * <AUTHOR>
  * @param softwareBuilds - Array of software builds to process
  * @returns Processed array with formatted dates
  */
  private processSoftwareBuildDates(softwareBuilds: SoftwareBuildListResponse[]): any[] {
    return softwareBuilds.map(softwareBuild => {
      if (softwareBuild.createdDate) {
        const formattedDate = this.datePipe.transform(new Date(softwareBuild.createdDate), 'MMM d, y, h:mm:ss a');
        return { ...softwareBuild, createdDate: formattedDate };
      }
      return softwareBuild;
    });
  }

  // ==================== SOFTWARE BUILD OPERATION METHODS ====================

  /**
  * Download attachment or release note
  * <AUTHOR>
  * @param id - Software build ID
  * @param attachmentType - Type of attachment ("attachment" or "releaseNote")
  * @param resourceName - Resource name for loading management
  * @returns Promise<string> - Download URL
  */
  public async downloadAttachment(id: number, attachmentType: string, resourceName: string): Promise<string> {
    try {
      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.softwareBuildApiCallService.getAttachmentUrl(id, { attachmentType: attachmentType }));

      this.isLoading(false, resourceName);
      return response.body.urlToDownload;

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      this.isLoading(false, resourceName);
      throw error;
    }
  }

  /**
  * Delete software build
  * <AUTHOR>
  * @param inventoryItemId - Software build ID to delete
  * @param itemNumber - Item number for confirmation
  * @param resourceName - Resource name for loading management
  */
  public async deleteSoftwareBuild(inventoryItemId: number, itemNumber: string, resourceName: string): Promise<boolean> {
    try {
      // Show confirmation dialog
      const confirmed = await this.softWareBuildConformationService.openInventoryOperationModel("Delete", "Cancel", "Delete", `Are you sure you want to delete ${itemNumber}? Doing same will remove the release association with test device if any.`);

      if (!confirmed) {
        return false;
      }

      if (!this.permissionService.getSoftwearBuildPermission(PermissionAction.DELETE_SOFTWARE_BUILD_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE);
        return false;
      }

      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.softwareBuildApiCallService.deleteSoftwearBuild(inventoryItemId));

      if (response.status === 200) {
        this.toastrService.success(response.body.message);
        this.showSuccessAndRefresh(resourceName);
      } else {
        this.isLoading(false, resourceName);
      }
      return true;

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  /**
  * Software Build Operation Action
  * <AUTHOR>
  * @param operationName
  * @param resourceName
  * @param selectedSoftwareBuildIdList
  * @param selectedSoftwareBuildList
  */
  public changeOperationForSoftwareBuild(operationName: string, resourceName: string, selectedSoftwareBuildIdList: number[], selectedSoftwareBuildList: SoftwareBuildListResponse[]): void {
    if (selectedSoftwareBuildIdList.length == 0) {
      this.toastrService.info("Please Select Software Build(s)");
    } else {
      switch (operationName) {
        case SoftwareBuildOperations.MAP_TO_CLIENT_DEVICES:
          this.mapWithDeviceType(selectedSoftwareBuildIdList, selectedSoftwareBuildList, [deviceTypesEnum.CLIENT_DEVICE], resourceName).then(res => {
            if (res) this.showSuccessAndRefresh(resourceName);
          });
          break;
        case SoftwareBuildOperations.MAP_TO_DEMO_DEVICES:
          this.mapWithDeviceType(selectedSoftwareBuildIdList, selectedSoftwareBuildList, [deviceTypesEnum.DEMO_DEVICE], resourceName).then(res => {
            if (res) this.showSuccessAndRefresh(resourceName);
          });
          break;
        case SoftwareBuildOperations.MAP_TO_BOTH_TYPE_OF_DEVICES:
          this.mapWithDeviceType(selectedSoftwareBuildIdList, selectedSoftwareBuildList, [deviceTypesEnum.CLIENT_DEVICE, deviceTypesEnum.DEMO_DEVICE], resourceName).then(res => {
            if (res) this.showSuccessAndRefresh(resourceName);
          });
          break;
        case SoftwareBuildOperations.MARK_AS_ACTIVE:
          this.changeInventoryStatus(selectedSoftwareBuildIdList, selectedSoftwareBuildList, true, resourceName).then(res => {
            if (res) this.showSuccessAndRefresh(resourceName);
          });
          break;
        case SoftwareBuildOperations.MARK_AS_INACTIVE:
          this.changeInventoryStatus(selectedSoftwareBuildIdList, selectedSoftwareBuildList, false, resourceName).then(res => {
            if (res) this.showSuccessAndRefresh(resourceName);
          });

          break;
        default:
          break;
      }
    }
  }

  /**
  * Map Software Build with Device Types
  * <AUTHOR>
  * @param softwareBuildIds - Array of software build IDs to map
  * @param selectedSoftwareBuilds - Array of selected software build objects for validation
  * @param deviceTypes - Array of device types to map to
  * @param resourceName - Resource name for validation
  */
  public async mapWithDeviceType(softwareBuildIds: number[], selectedSoftwareBuilds: SoftwareBuildListResponse[], deviceTypes: deviceTypesEnum[], resourceName: string): Promise<boolean> {
    try {
      // Validate user permissions and country access
      if (!this.validateSoftwareBuildPermissions(softwareBuildIds, selectedSoftwareBuilds, resourceName)) {
        return false;
      }

      if (!this.permissionService.getSoftwearBuildPermission(PermissionAction.UPDATE_SOFTWARE_BUILD_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE);
        return false;
      }

      const confirmed = await this.softWareBuildConformationService.openInventoryOperationModel("Confirm", "Cancel", "Confirmation", this.commonsService.getDeviceTypeMapMessage(deviceTypes));

      if (!confirmed) {
        return false;
      }

      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.softwareBuildApiCallService.mapInventoryWithDeviceType(softwareBuildIds, { deviceType: deviceTypes }));
      this.toastrService.success(response.body['message']);

      return true;

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  /**
  * Change Software Build Status (Active/Inactive)
  * <AUTHOR>
  * @param softwareBuildIds - Array of software build IDs to change status
  * @param selectedSoftwareBuilds - Array of selected software build objects for validation
  * @param isActive - true for active, false for inactive
  * @param resourceName - Resource name for validation
  */
  public async changeInventoryStatus(softwareBuildIds: number[], selectedSoftwareBuilds: SoftwareBuildListResponse[], isActive: boolean, resourceName: string): Promise<boolean> {
    try {
      // Validate user permissions and country access
      if (!this.validateSoftwareBuildPermissions(softwareBuildIds, selectedSoftwareBuilds, resourceName)) {
        return false;
      }

      if (!this.permissionService.getSoftwearBuildPermission(PermissionAction.UPDATE_SOFTWARE_BUILD_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE);
        return false;
      }

      const confirmed = await this.softWareBuildConformationService.openInventoryOperationModel("Confirm", "Cancel", "Confirmation", "Are you sure you want to mark Software Build(s) as " + (isActive ? "Active?" : "Inactive? Doing same will remove the release association with test device if any."));

      if (!confirmed) {
        return false;
      }

      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.softwareBuildApiCallService.markInventoriesActiveInactive(softwareBuildIds, { active: isActive }));
      this.toastrService.success(response.body['message']);
      return true;

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  /**
  * Validate software build selection and permissions
  * <AUTHOR>
  * @param softwareBuildIds - Array of software build IDs
  * @param selectedSoftwareBuilds - Array of selected software build objects
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  private validateSoftwareBuildPermissions(softwareBuildIds: number[], selectedSoftwareBuilds: SoftwareBuildListResponse[], resourceName: string): boolean {
    if (softwareBuildIds.length === 0) {
      this.toastrService.info("Please Select Software Build(s)");
      return false;
    }

    // Get associated countries for validation
    const itemAssociatedCountryList = this.getItemAssociatedCountryList(selectedSoftwareBuilds);
    return this.moduleValidationService.validateWithUserCountryForMultileRecord(itemAssociatedCountryList, resourceName, true);
  }

  /**
  * Get Item Associated Country List
  * <AUTHOR>
  * @param selectedSoftwareBuilds - Array of selected software build objects
  * @returns Array of associated country names
  */
  private getItemAssociatedCountryList(selectedSoftwareBuilds: SoftwareBuildListResponse[]): string[] {
    let itemAssociatedCountryList: Array<string> = [];
    for (let softwareBuild of selectedSoftwareBuilds) {
      if (softwareBuild.countries && softwareBuild.countries.length > 0) {
        itemAssociatedCountryList = softwareBuild.countries;
      }
    }
    return itemAssociatedCountryList;
  }

  /**
  * Show success message and refresh page based on resource type
  * <AUTHOR>
  * @param message - Success message to display
  * @param resourceName - Resource name for determining refresh action
  */
  private showSuccessAndRefresh(resourceName: string): void {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    this.callRefreshPageSubject(listingPageReloadSubjectParameter, resourceName, false, null);
  }

  /**
  * Download JSON file for software build
  * <AUTHOR>
  * @param jsonMasterId - JSON master ID to download
  * @param version - Version name for the file
  * @param resourceName - Resource name for loading management
  */
  public async downloadJSONFile(jsonMasterId: number, version: string, resourceName: string): Promise<void> {
    try {
      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.videoService.downloadJSONFile(jsonMasterId));

      if (response && response.body && response.body["data"]) {
        const jsonData = response.body["data"];
        const blob = new Blob([JSON.stringify(jsonData)], { type: 'application/json' });
        const downloadLink = document.createElement('a');
        downloadLink.href = window.URL.createObjectURL(new Blob([blob], { type: blob.type }));
        downloadLink.setAttribute('download', `${version}.json`);
        document.body?.appendChild(downloadLink);
        downloadLink.click();
        document.body?.removeChild(downloadLink);
      }

      this.isLoading(false, resourceName);
      return;

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      this.isLoading(false, resourceName);
      return;
    }
  }

  // ==================== FILTER INPUT/OUTPUT PROCESSING METHODS ====================

  /**
  * Validate software build filter form data
  * Checks if at least one filter field has a value
  * <AUTHOR>
  * @param formValue - Filter form values
  * @returns true if form has valid filter data, false otherwise
  */
  public validateFilterForm(formValue: any): boolean {
    const itemNumberEmpty = this.commonsService.checkValueIsNullOrEmpty(formValue?.value.itemNumber);
    const deviceTypeEmpty = this.commonsService.checkValueIsNullOrEmpty(formValue?.value.deviceType);
    const countryEmpty = this.commonsService.checkValueIsNullOrEmpty(formValue?.value.country);
    const jsonVersionsEmpty = this.commonsService.checkValueIsNullOrEmpty(formValue?.value.jsonVersions);
    const inventoryStatusEmpty = this.commonsService.checkValueIsNullOrEmpty(formValue?.value.inventoryStatus);
    const partNumberEmpty = this.commonsService.checkValueIsNullOrEmpty(formValue?.value.partNumber);

    const allFieldsEmpty = itemNumberEmpty && deviceTypeEmpty && countryEmpty &&
      jsonVersionsEmpty && inventoryStatusEmpty && partNumberEmpty;
    return !allFieldsEmpty;
  }

  /**
  * Build software build search request from filter form data
  * Processes form values and creates SoftwareBuildSearchRequestBody object
  * <AUTHOR>
  * @param formValue - Filter form values
  * @returns SoftwareBuildSearchRequestBody object
  */
  public buildSoftwareBuildSearchRequest(formValue: any): SoftwareBuildSearchRequestBody {
    // Process text fields
    const itemNumber = this.commonsService.checkNullFieldValue(formValue.value.itemNumber);
    const partNumber = this.commonsService.checkNullFieldValue(formValue.value.partNumber);

    // Process country selection
    const countryValue = formValue.value.country;
    const countryIds: number[] = isNullOrUndefined(countryValue) ? null : this.commonsService.getIdsFromArray(countryValue);

    // Process JSON version selection
    const jsonVersionValue = formValue.value.jsonVersions;
    const jsonVersionIds: number[] = isNullOrUndefined(jsonVersionValue) ? null : this.commonsService.getIdsFromArray(jsonVersionValue);

    // Process device type selection
    const deviceType = this.commonsService.getDeviceTypeFilterValueArray(formValue, 'deviceType');

    // Process inventory status selection
    const isActive = this.commonsService.getValuesFromArray(formValue, 'inventoryStatus');

    return new SoftwareBuildSearchRequestBody(
      itemNumber,
      countryIds,
      deviceType,
      isActive,
      jsonVersionIds,
      partNumber
    );
  }

  /**
  * Process filter search request
  * Validates form and triggers software build list refresh
  * <AUTHOR>
  * @param formValue - Filter form values
  * @param isFormInvalid - Whether the form is invalid
  * @param listingPageReloadSubjectParameter - Page reload parameters
  * @returns true if search was processed, false if validation failed
  */
  public processFilterSearch(formValue: any, isFormInvalid: boolean, listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): boolean {
    const isValidForm = this.validateFilterForm(formValue);

    if (isFormInvalid || !isValidForm) {
      this.toastrService.info(COMMON_SELECT_FILTER);
      return false;
    }

    const softwareBuildSearchRequest = this.buildSoftwareBuildSearchRequest(formValue);

    const softwareBuildFilterAction = new SoftwareBuildFilterAction(listingPageReloadSubjectParameter, softwareBuildSearchRequest);

    this.callSoftwareBuildListFilterRequestParameterSubject(softwareBuildFilterAction);
    return true;
  }

}
