<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
  <!-- loading gif start -->
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
  <!-- loading gif end -->
</div>
<!-- loading end -->

<!-- name : customeremail assoc component -->
<!-- description : use for customeremail association -->
<!----------------------------------------------------->
<!----------------------------------------------------->
<!-- dialog title - start -->
<div class="modal-header">
  <label class="modal-title">{{ customerAssociationModelRequest?.title }}</label>
</div>
<!-- dialog title - end -->
<!----------------------------------------------------->

<!----------------------------------------------------->
<!-- dialog body - start -->
<!----------------------------------------------------->
<div class="modal-body">
  <div>
    <!----------------------------------------------------->
    <!-- Map Customer association form - start -->
    <!----------------------------------------------------->
    <form name='salesOrderInfo' [formGroup]="form">
      <!----------------------------------------------------->
      <!-- map Customer association table - start -->
      <!----------------------------------------------------->
      <table class="customer-association-table" aria-hidden="true">
        <!----------------------------------------------------->
        <!-- sales order number formfield - start -->
        <!----------------------------------------------------->
        <tr>
          <th class="upload-title" id="soNumber">
            <span class="pr-2">Sales Order Number</span>
          </th>
          <td>
            <ng-multiselect-dropdown class="devicePageDeviceType sales-order-control" id="salesOrderNumberField"
              name="salesOrderNumber" [placeholder]="''" (click)="onItemClickValidation('salesOrderNumber')"
              (onSelect)="onDropdownSelect($event)" (onDeSelect)="onDropdownDeSelect()"
              formControlName="salesOrderNumber" [disabled]="isSalesOrderDisabled" [settings]="dropdownSettings"
              [data]="salesOrderNumberList" id="salesOrderNumberField">
            </ng-multiselect-dropdown>
            <button class=" btn btn-sm btn-orange radius-50 ml-2" (click)="openManualSalesOrderField()"
              id="addSalesOrderBtn">
              <em class="fa fa-plus"></em>
            </button>
          </td>
        </tr>
        <tr>
          <td></td>
          <td>
            <div
              *ngIf="(form.get('salesOrderNumber').touched || form.get('salesOrderNumber').dirty) && form.get('salesOrderNumber').invalid ">
              <!-- required sales order number -->
              <div *ngIf="form.get('salesOrderNumber').errors['required']">
                <span class="alert-color font-12"> Sales Order Number is required</span>
              </div>
            </div>
          </td>
        </tr>
        <!--------------------------------------------------->
        <!-- sales order number formfield - end -->
        <!--------------------------------------------------->

        <!--------------------------------------------------->
        <!-- sales order number formfield - start -->
        <!--------------------------------------------------->
        <tr>
          <td></td>
          <td>
            <div class="manual-sales-order-control" id="salesOrderField">
              <input tabindex="-1" class="form-control sales-order-control mt-2" type="text"
                formControlName="manualSalesOrderNumber" (input)="setDisableStatus()" />
              <button class="btn btn-sm btn-orange radius-50 close-field ml-2 mt-2"
                (click)="closeManualSalesOrderField()" id="closeSalesOrderField">
                <em class="fa fa-close"></em>
              </button>
            </div>
          </td>
        </tr>
        <tr>
          <td></td>
          <td>
            <!--------------------------------------------------->
            <!-- required sales order number -->
            <!-- sales order number maxlength validation error-->
            <!--------------------------------------------------->
            <div
              *ngIf="(form.get('manualSalesOrderNumber').touched  || form.get('manualSalesOrderNumber').dirty) && form.get('manualSalesOrderNumber').invalid ">
              <div *ngIf="form.get('manualSalesOrderNumber').errors['required']">
                <span class="alert-color font-12"> Sales Order Number is required</span>
              </div>
              <div *ngIf="form.controls['manualSalesOrderNumber'].hasError('maxlength')">
                <span class="alert-color font-12">{{small_textBoxMaxCharactersAllowedMessage}}</span>
              </div>
            </div>
            <div class="mb-2"></div>
          </td>
        </tr>
        <!--------------------------------------------------->
        <!-- sales order number formfield - end -->
        <!--------------------------------------------------->

        <tr>
          <th class="upload-title" id="cust_name">
            <span class="pr-2">Customer Name</span>
          </th>
          <td>
            <!-- customer name formfield - start -->
            <input tabindex="-1" class="form-control" type="text" formControlName="customerName" id="customerNameField"
              (input)="setDisableStatus()" [readOnly]="isDisable" />
            <!-- customer name formfield - end -->
          </td>
        </tr>
        <tr>
          <td></td>
          <td>
            <div
              *ngIf="(form.get('customerName').touched || form.get('customerName').dirty) && form.get('customerName').invalid ">
              <!-- required customer name -->
              <div *ngIf="form.get('customerName').errors['required']">
                <span class="alert-color font-12"> {{enterCustomerName}}</span>
              </div>
              <!-- customer name maxlength validation error-->
              <div *ngIf="form.controls['customerName'].hasError('maxlength')">
                <span class="alert-color font-12">{{textBoxMaxCharactersAllowedMessage}}</span>
              </div>
              <div *ngIf="form.controls['customerName'].hasError('pattern')">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
              </div>
            </div>
            <div class="mb-2"></div>
          </td>
        </tr>

        <tr>
          <th class="upload-title" id="cust_email">
            <span class="pr-2">Customer E-mail</span>
          </th>
          <td>
            <!-- customer email formfield - start -->
            <input tabindex="-1" class="form-control" type="text" formControlName="customerEmail"
              id="customerEmailField" (input)="setDisableStatus()" [readOnly]="isDisable" />
            <!-- customer email formfield - end -->
          </td>
        </tr>
        <tr>
          <td></td>
          <td>
            <div
              *ngIf="(form.get('customerEmail').touched || form.get('customerEmail').dirty) && form.get('customerEmail').invalid ">
              <!-- required email validation error -->
              <div *ngIf="form.get('customerEmail').errors['required']">
                <span class="alert-color font-12">{{enterEmail}}</span>
              </div>
              <!-- email pattern validation error-->
              <div
                *ngIf="form.get('customerEmail').errors['pattern'] && !(form.controls['customerEmail'].hasError('maxlength'))">
                <span class="alert-color font-12"> {{enterValidEmail}}</span>
              </div>
              <!-- customer email maxlength validation error-->
              <div *ngIf="form.controls['customerEmail'].hasError('maxlength')">
                <span class="alert-color font-12">{{textBoxMaxCharactersAllowedMessage}}</span>
              </div>
            </div>
            <div class="mb-2"></div>
          </td>
        </tr>

        <tr>
          <th class="upload-title" id="soNumber">
            <span class="pr-2">Country</span>
          </th>
          <td>
            <ng-multiselect-dropdown class="form-select" id="countryField" name="country" [placeholder]="''"
              (click)="onItemClickValidation('country')" formControlName="country" [settings]="countryDropDownSetting"
              formControlName="country" [data]="countryList" [disabled]="isDisable">
            </ng-multiselect-dropdown>
          </td>
        </tr>
        <tr>
          <td></td>
          <td>
            <div *ngIf="(form.get('country').touched ) && form.get('country').invalid ">
              <!-- required email validation error -->
              <div *ngIf="form.get('country').errors['required']">
                <span class="alert-color font-12">{{countryValidMessage}}</span>
              </div>
            </div>
            <div class="mb-2"></div>
          </td>
        </tr>


        <!--------------------------------------------------->
        <!------ Order Record Type form field - Start ------->
        <!--------------------------------------------------->

        <tr>
          <th class="upload-title" id="orderRecordType">
            <span class="pr-2">Order Record Type</span>
          </th>
          <td>
            <ng-multiselect-dropdown class="form-select" id="orderRecordType" name="orderRecordType" [placeholder]="''"
              (click)="onItemClickValidation('orderRecordType')" formControlName="orderRecordType"
              [settings]="orderRecordTypeSetting" [data]="orderRecordTypeList" [disabled]="isDisable">
            </ng-multiselect-dropdown>
          </td>
        </tr>
        <tr>
          <td></td>
          <td>
            <div *ngIf="(form.get('orderRecordType').touched ) && form.get('orderRecordType').invalid ">
              <!-- required Order Record Type validation error -->
              <div *ngIf="form.get('orderRecordType').errors['required']">
                <span class="alert-color font-12">{{orderRecordTypeMessage}}</span>
              </div>
            </div>
            <div class="mb-2"></div>
          </td>
        </tr>

        <!--------------------------------------------------->
        <!------ Order Record Type form field - End --------->
        <!--------------------------------------------------->
        <tr>
          <th class="upload-title" id="cust_name">
            <span class="pr-2">PO#</span>
          </th>
          <td>
            <!-- Po Number formfield - start -->
            <input tabindex="-1" class="form-control" type="text" formControlName="poNumber" id="customerNameField"
              (input)="setDisableStatus()" [readOnly]="isDisable" />
            <!--  Po Number formfield - end -->
          </td>
        </tr>
        <tr>
          <td></td>
          <td>
            <div *ngIf="(form.get('poNumber').touched || form.get('poNumber').dirty) && form.get('poNumber').invalid ">
              <!-- po number maxlength validation error-->
              <div *ngIf="form.controls['poNumber'].hasError('maxlength')">
                <span class="alert-color font-12">{{textBoxMaxCharactersAllowedMessage}}</span>
              </div>
            </div>
            <div class="mb-2"></div>
          </td>
        </tr>

        <tr>
          <td colspan="2">
            <div class="d-flex align-baseline">
              <!-- Section for Device Auto Lock -->
              <div class="d-flex align-items-center w-50 pe-2">
                <!-- Label for Device Auto Lock -->
                <label for="deviceAutoLockField" class="upload-title text-nowrap m-0">
                  <span class="text-center" style="font-weight: bolder">Device Auto Lock</span>
                </label>
                <!-- Checkbox for Device Auto Lock, bound to the form control -->
                <input tabindex="-1" type="checkbox" class="ms-2 deviceAutoLockField" formControlName="deviceAutoLock"
                  id="deviceAutoLockField" [checked]="form.get('deviceAutoLock').value"
                  [attr.disabled]="isDisable?isDisable:null" />
              </div>

              <!-- Section for Probe Auto Lock -->
              <div class="d-flex align-items-center w-50 ps-2">
                <!-- Label for Probe Auto Lock -->
                <label for="probeAutoLockField" class="upload-title m-0 text-nowrap">
                  <span class="fw-bold text-center" style="font-weight: bolder">Probe Auto Lock</span>
                </label>
                <!-- Checkbox for Probe Auto Lock, bound to the form control -->
                <input tabindex="-1" type="checkbox" class="ms-2 probeAutoLockField" formControlName="probeAutoLock"
                  id="probeAutoLockField" [checked]="form.get('probeAutoLock').value"
                  [attr.disabled]="isDisable?isDisable:null" />
              </div>
            </div>
          </td>
        </tr>

      </table>
      <!----------------------------------------------------->
      <!-- map Customer association table end -->
      <!----------------------------------------------------->
    </form>
    <!----------------------------------------------------->
    <!-- Map Customer association form - end -->
    <!----------------------------------------------------->
  </div>
</div>
<!----------------------------------------------------->
<!-- dialog body - end -->
<!----------------------------------------------------->

<!----------------------------------------------------->
<!-- dialog buttons - start -->
<!-- cancel button -->
<!-- accept button -->
<!----------------------------------------------------->
<hr class="pb-2" />
<div class=" d-flex align-items-center justify-content-between">
  <div> <span class=text-primary *ngIf="isDisable">*Order details cannot be updated as it is associated with a
      Salesforce-generated order.
    </span>
  </div>

  <div class="d-flex mb-1 justify-content-end">
    <button type="button" tabindex="-1" class="btn btn-sm btn-outline-secondary mx-1" (click)="decline()">{{
      customerAssociationModelRequest?.btnCancelText }}</button>
    <button type="button" class="btn btn-sm btn-orange" id="uploadBtn" (click)="accept()"
      [disabled]="!form.valid || isButtonDisable || isDisable">{{ customerAssociationModelRequest?.btnOkText
      }}</button>

  </div>
  <!----------------------------------------------------->
  <!-- dialog buttons - end -->
  <!----------------------------------------------------->