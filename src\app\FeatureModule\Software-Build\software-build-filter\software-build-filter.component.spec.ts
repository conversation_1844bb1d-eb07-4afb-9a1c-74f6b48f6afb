import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse } from '@angular/common/http';
import { of, Subject, throwError } from 'rxjs';
import { SoftwareBuildFilterComponent } from './software-build-filter.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { SoftwareBuildOperationsService } from '../software-build-services/software-build-operations.service';
import { DatePipe } from '@angular/common';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { DeviceOperationService } from '../../Device/DeviceService/Device-Operation/device-operation.service';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { VideoService } from 'src/app/shared/videoservice/video.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { SoftwareBuildStatusEnum } from 'src/app/shared/enum/SoftwareBuildStatusEnum';
import { ToastrService } from 'ngx-toastr';
import { ConfirmDialogService } from '../../CommonComponent/confirmationdialog/confirmation.service';

describe('SoftwareBuildFilterComponent', () => {
  let component: SoftwareBuildFilterComponent;
  let fixture: ComponentFixture<SoftwareBuildFilterComponent>;
  let videoService: jasmine.SpyObj<VideoService>;
  let countryCacheService: jasmine.SpyObj<CountryCacheService>;
  let exceptionService: jasmine.SpyObj<ExceptionHandlingService>;
  let softwareBuildOperationsService: jasmine.SpyObj<SoftwareBuildOperationsService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;


  const mockCountryList: CountryListResponse[] = [
    new CountryListResponse(1, 'USA', ['English']),
    new CountryListResponse(2, 'Canada', ['English', 'French'])
  ];

  const mockJsonVersionList: Jsonlist[] = [
    new Jsonlist(1, '1.0.0'),
    new Jsonlist(2, '2.0.0')
  ];

  const mockSoftwareBuildSearchRequestBody = new SoftwareBuildSearchRequestBody(
    'test-version',
    [1, 2],
    deviceTypesEnum.CLIENT_DEVICE,
    true,
    [1],
    'test-part'
  );

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const videoServiceSpy = jasmine.createSpyObj('VideoService', ['getListofJsonVersions']);
    const countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const softwareBuildOperationsServiceSpy = jasmine.createSpyObj('SoftwareBuildOperationsService', [
      'getJsonVersionList', 'setJsonVersionList', 'getCountryList', 'setCountryList',
      'getSoftwareBuildListRefreshSubject', 'callSoftwareBuildListFilterRequestParameterSubject',
      'getSoftwareBuildSearchRequestBodyForListingApi', 'getListPageRefreshForbackToOtherPage',
      'clearAllFiltersAndRefresh', 'buildSoftwareBuildSearchRequest', 'processFilterSearch',
      'setListPageRefreshForbackToOtherPage'
    ]);


    await TestBed.configureTestingModule({
      declarations: [SoftwareBuildFilterComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        { provide: VideoService, useValue: videoServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        { provide: SoftwareBuildOperationsService, useValue: softwareBuildOperationsServiceSpy },
        CommonsService,
        CommonOperationsService,
        MultiSelectDropDownSettingService,
        LocalStorageService,
        SessionStorageService,
        DatePipe,
        RoleApiCallService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        DeviceOperationService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildFilterComponent);
    component = fixture.componentInstance;

    videoService = TestBed.inject(VideoService) as jasmine.SpyObj<VideoService>;
    countryCacheService = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    exceptionService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    softwareBuildOperationsService = TestBed.inject(SoftwareBuildOperationsService) as jasmine.SpyObj<SoftwareBuildOperationsService>;

    // Setup default mock returns
    softwareBuildOperationsService.getJsonVersionList.and.returnValue([]);
    softwareBuildOperationsService.getCountryList.and.returnValue([]);
    softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(new Subject());
    softwareBuildOperationsService.getSoftwareBuildSearchRequestBodyForListingApi.and.returnValue(null);
    softwareBuildOperationsService.getListPageRefreshForbackToOtherPage.and.returnValue(false);
    softwareBuildOperationsService.processFilterSearch.and.returnValue(true);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component with API call when isFilterComponentInitWithApicall is true and listPageRefreshForbackToDetailPage is false', () => {
      // Setup
      component.isFilterComponentInitWithApicall = true;
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
      softwareBuildOperationsService.getListPageRefreshForbackToOtherPage.and.returnValue(false);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component, 'clearFilter');
      spyOn(component, 'getInitCall').and.returnValue(Promise.resolve());
      spyOn(component, 'onInitSubject');
      spyOn(component as any, 'initializeDropdownSettings');

      // Act
      component.ngOnInit();

      // Assert
      expect(component.clearFilter).toHaveBeenCalled();
      expect(component['initializeDropdownSettings']).toHaveBeenCalled();
      expect(component.getInitCall).toHaveBeenCalled();
      expect(component.onInitSubject).toHaveBeenCalled();
    });

    it('should initialize component without API call when isFilterComponentInitWithApicall is false', () => {
      // Setup
      component.isFilterComponentInitWithApicall = false;
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
      softwareBuildOperationsService.getListPageRefreshForbackToOtherPage.and.returnValue(false);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component, 'clearFilter');
      spyOn(component, 'getInitCall').and.returnValue(Promise.resolve());
      spyOn(component, 'onInitSubject');
      spyOn(component as any, 'initializeDropdownSettings');

      // Act
      component.ngOnInit();

      // Assert
      expect(component.clearFilter).not.toHaveBeenCalled();
      expect(component['initializeDropdownSettings']).toHaveBeenCalled();
      expect(component.getInitCall).toHaveBeenCalled();
      expect(component.onInitSubject).toHaveBeenCalled();
    });

    it('should not call clearFilter when listPageRefreshForbackToDetailPage is true', () => {
      // Setup
      component.isFilterComponentInitWithApicall = true;
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
      softwareBuildOperationsService.getListPageRefreshForbackToOtherPage.and.returnValue(true);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component, 'clearFilter');
      spyOn(component, 'getInitCall').and.returnValue(Promise.resolve());
      spyOn(component, 'onInitSubject');
      spyOn(component as any, 'initializeDropdownSettings');

      // Act
      component.ngOnInit();

      // Assert
      expect(component.clearFilter).not.toHaveBeenCalled();
    });
  });

  describe('initializeDropdownSettings', () => {
    it('should initialize all dropdown settings', () => {
      // Setup
      const multiSelectService = TestBed.inject(MultiSelectDropDownSettingService);
      spyOn(multiSelectService, 'getCountryDrpSetting').and.returnValue({} as any);
      spyOn(multiSelectService, 'getDeviceTypeDropdownSetting').and.returnValue({} as any);
      spyOn(multiSelectService, 'getSoftwareStatusDropdownSetting').and.returnValue({} as any);
      spyOn(multiSelectService, 'getjsonVersionDropdownSetting').and.returnValue({} as any);

      // Act
      component['initializeDropdownSettings']();

      // Assert
      expect(multiSelectService.getCountryDrpSetting).toHaveBeenCalledWith(false, false);
      expect(multiSelectService.getDeviceTypeDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getSoftwareStatusDropdownSetting).toHaveBeenCalled();
      expect(multiSelectService.getjsonVersionDropdownSetting).toHaveBeenCalledWith(false);
      expect(component.dropdownSettingsCountry).toBeDefined();
      expect(component.dropdownSettingsDeviceType).toBeDefined();
      expect(component.dropdownSettingsInventoryStatus).toBeDefined();
      expect(component.dropdownSettingsJsonVersions).toBeDefined();
    });
  });

  describe('getInitCall', () => {
    it('should initialize device types and inventory status', async () => {
      // Setup
      softwareBuildOperationsService.getJsonVersionList.and.returnValue(mockJsonVersionList);
      softwareBuildOperationsService.getCountryList.and.returnValue(mockCountryList);
      spyOn(component as any, 'setFilterValue');

      // Act
      await component.getInitCall();

      // Assert
      expect(component.deviceTypes).toEqual(['Client Device', 'Demo Device', 'Above Both', 'Not Associated']);
      expect(component.inventoryStatus).toEqual([SoftwareBuildStatusEnum.ACTIVE, SoftwareBuildStatusEnum.INACTIVE]);
      expect(component.jsonVersionList).toBe(mockJsonVersionList);
      expect(component.countryList).toBe(mockCountryList);
      expect(component['setFilterValue']).toHaveBeenCalled();
    });

    it('should call getJsonVersion when service has empty json version list', async () => {
      // Setup
      softwareBuildOperationsService.getJsonVersionList.and.returnValue([]);
      softwareBuildOperationsService.getCountryList.and.returnValue(mockCountryList);
      spyOn(component as any, 'getJsonVersion');
      spyOn(component as any, 'setFilterValue');

      // Act
      await component.getInitCall();

      // Assert
      expect(component['getJsonVersion']).toHaveBeenCalled();
    });

    it('should call getCountryList when service has empty country list', async () => {
      // Setup
      softwareBuildOperationsService.getJsonVersionList.and.returnValue(mockJsonVersionList);
      softwareBuildOperationsService.getCountryList.and.returnValue([]);
      spyOn(component as any, 'getCountryList').and.returnValue(Promise.resolve());
      spyOn(component as any, 'setFilterValue');

      // Act
      await component.getInitCall();

      // Assert
      expect(component['getCountryList']).toHaveBeenCalled();
    });
  });

  describe('getJsonVersion', () => {
    it('should fetch and cache json versions successfully', () => {
      // Setup
      const mockResponse = new HttpResponse({ body: mockJsonVersionList });
      videoService.getListofJsonVersions.and.returnValue(of(mockResponse));

      // Act
      component['getJsonVersion']();

      // Assert
      expect(videoService.getListofJsonVersions).toHaveBeenCalled();
      expect(softwareBuildOperationsService.setJsonVersionList).toHaveBeenCalledWith(mockJsonVersionList);
      expect(component.jsonVersionList).toEqual(mockJsonVersionList);
    });

    it('should handle error when fetching json versions', () => {
      // Setup
      const mockError = new Error('API Error');
      videoService.getListofJsonVersions.and.returnValue(throwError(() => mockError));

      // Act
      component['getJsonVersion']();

      // Assert
      expect(exceptionService.customErrorMessage).toHaveBeenCalledWith(jasmine.any(Error));
    });
  });

  describe('getCountryList', () => {
    it('should fetch and cache country list successfully', async () => {
      // Setup
      countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(mockCountryList));

      // Act
      await component['getCountryList']();

      // Assert
      expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
      expect(softwareBuildOperationsService.setCountryList).toHaveBeenCalledWith(mockCountryList);
      expect(component.countryList).toBe(mockCountryList);
    });
  });

  describe('onInitSubject', () => {
    it('should subscribe to refresh subject and handle clear filter', () => {
      // Setup
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component, 'clearFilter');

      // Act
      component.onInitSubject();
      mockSubject.next(mockParameter);

      // Assert
      expect(component.clearFilter).toHaveBeenCalled();
    });

    it('should subscribe to refresh subject and handle page refresh', () => {
      // Setup
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component.onInitSubject();
      mockSubject.next(mockParameter);

      // Assert
      expect(component['softWareBuildPageRefresh']).toHaveBeenCalledWith(mockParameter);
    });

    it('should not process when isReloadData is false', () => {
      // Setup
      const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
      const mockParameter = new ListingPageReloadSubjectParameter(false, true, false, false);
      softwareBuildOperationsService.getSoftwareBuildListRefreshSubject.and.returnValue(mockSubject);
      spyOn(component, 'clearFilter');
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component.onInitSubject();
      mockSubject.next(mockParameter);

      // Assert
      expect(component.clearFilter).not.toHaveBeenCalled();
      expect(component['softWareBuildPageRefresh']).not.toHaveBeenCalled();
    });
  });

  describe('clearFilter', () => {
    it('should reset form and call clearAllFiltersAndRefresh', () => {
      // Setup
      spyOn(component.filterForm, 'reset');

      // Act
      component.clearFilter();

      // Assert
      expect(component.filterForm.reset).toHaveBeenCalled();
      expect(softwareBuildOperationsService.clearAllFiltersAndRefresh).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });
  });

  describe('softWareBuildPageRefresh', () => {
    it('should reset form when invalid and process filter data', () => {
      // Setup
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      spyOn(component.filterForm, 'reset');
      Object.defineProperty(component.filterForm, 'invalid', { value: true, writable: true });
      const mockSearchRequest = new SoftwareBuildSearchRequestBody('test', [], null, null, null, null);
      softwareBuildOperationsService.buildSoftwareBuildSearchRequest.and.returnValue(mockSearchRequest);

      // Act
      component['softWareBuildPageRefresh'](mockParameter);

      // Assert
      expect(component.filterForm.reset).toHaveBeenCalled();
      expect(softwareBuildOperationsService.buildSoftwareBuildSearchRequest).toHaveBeenCalledWith(component.filterForm);
      expect(softwareBuildOperationsService.callSoftwareBuildListFilterRequestParameterSubject).toHaveBeenCalled();
    });

    it('should process valid form data without reset', () => {
      // Setup
      const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      Object.defineProperty(component.filterForm, 'invalid', { value: false, writable: true });
      const mockSearchRequest = new SoftwareBuildSearchRequestBody('test', [], null, null, null, null);
      softwareBuildOperationsService.buildSoftwareBuildSearchRequest.and.returnValue(mockSearchRequest);
      spyOn(component.filterForm, 'reset');

      // Act
      component['softWareBuildPageRefresh'](mockParameter);

      // Assert
      expect(component.filterForm.reset).not.toHaveBeenCalled();
      expect(softwareBuildOperationsService.buildSoftwareBuildSearchRequest).toHaveBeenCalledWith(component.filterForm);
      expect(softwareBuildOperationsService.callSoftwareBuildListFilterRequestParameterSubject).toHaveBeenCalled();
    });
  });

  describe('searchInventoryFilter', () => {
    it('should process search when validation passes', () => {
      // Setup
      component.filterForm.patchValue({
        itemNumber: 'test-item',
        partNumber: 'test-part'
      });
      softwareBuildOperationsService.processFilterSearch.and.returnValue(true);

      // Act
      component.searchInventoryFilter();

      // Assert
      expect(softwareBuildOperationsService.processFilterSearch).toHaveBeenCalledWith(
        component.filterForm,
        component.filterForm.invalid,
        component.defaultListingPageReloadSubjectParameter
      );
    });

    it('should return early when validation fails', () => {
      // Setup
      component.filterForm.patchValue({
        itemNumber: '',
        partNumber: ''
      });
      softwareBuildOperationsService.processFilterSearch.and.returnValue(false);

      // Act
      component.searchInventoryFilter();

      // Assert
      expect(softwareBuildOperationsService.processFilterSearch).toHaveBeenCalled();
    });

    it('should set null values for empty text fields', () => {
      // Setup
      component.filterForm.patchValue({
        itemNumber: '  ',
        partNumber: '  '
      });
      const commonsService = TestBed.inject(CommonsService);
      spyOn(commonsService, 'checkNullFieldValue').and.returnValues(null, null);

      // Act
      component.searchInventoryFilter();

      // Assert
      expect(commonsService.checkNullFieldValue).toHaveBeenCalledTimes(2);
      expect(component.filterForm.get('itemNumber').value).toBe(null);
      expect(component.filterForm.get('partNumber').value).toBe(null);
    });
  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from subscription when it exists', () => {
      // Setup
      const mockSubscription = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component.subscriptionForRefreshList = mockSubscription;

      // Act
      component.ngOnDestroy();

      // Assert
      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });

    it('should not throw error when subscription is undefined', () => {
      // Setup
      component.subscriptionForRefreshList = undefined;

      // Act & Assert
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  describe('setFilterValue', () => {
    it('should set filter values when softwareBuildSearchRequestBody is provided', () => {
      // Setup
      component.softwareBuildSearchRequestBody = mockSoftwareBuildSearchRequestBody;
      component.countryList = mockCountryList;
      component.jsonVersionList = mockJsonVersionList;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('itemNumber').value).toBe('test-version');
      expect(component.filterForm.get('partNumber').value).toBe('test-part');
    });

    it('should handle null deviceType in search request body', () => {
      // Setup
      const searchRequestWithNullDeviceType = new SoftwareBuildSearchRequestBody(
        'test-version', [1, 2], null, true, [1], 'test-part'
      );
      component.softwareBuildSearchRequestBody = searchRequestWithNullDeviceType;
      component.countryList = mockCountryList;
      component.jsonVersionList = mockJsonVersionList;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('deviceType').value).toEqual([]);
    });

    it('should handle null isActive in search request body', () => {
      // Setup
      const searchRequestWithNullIsActive = new SoftwareBuildSearchRequestBody(
        'test-version', [1, 2], deviceTypesEnum.CLIENT_DEVICE, null, [1], 'test-part'
      );
      component.softwareBuildSearchRequestBody = searchRequestWithNullIsActive;
      component.countryList = mockCountryList;
      component.jsonVersionList = mockJsonVersionList;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('inventoryStatus').value).toEqual([]);
    });

    it('should handle empty jsonIds in search request body', () => {
      // Setup
      const searchRequestWithEmptyJsonIds = new SoftwareBuildSearchRequestBody(
        'test-version', [1, 2], deviceTypesEnum.CLIENT_DEVICE, true, [], 'test-part'
      );
      component.softwareBuildSearchRequestBody = searchRequestWithEmptyJsonIds;
      component.countryList = mockCountryList;
      component.jsonVersionList = mockJsonVersionList;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('jsonVersions').value).toEqual([]);
    });

    it('should call page refresh when listPageRefreshForbackToDetailPage is true', () => {
      // Setup
      component.listPageRefreshForbackToDetailPage = true;
      component.softwareBuildSearchRequestBody = null;
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component['setFilterValue']();

      // Assert
      expect(softwareBuildOperationsService.setListPageRefreshForbackToOtherPage).toHaveBeenCalledWith(false);
      expect(component['softWareBuildPageRefresh']).toHaveBeenCalledWith(component.defaultListingPageReloadSubjectParameter);
    });

    it('should not call page refresh when listPageRefreshForbackToDetailPage is false', () => {
      // Setup
      component.listPageRefreshForbackToDetailPage = false;
      component.softwareBuildSearchRequestBody = null;
      spyOn(component as any, 'softWareBuildPageRefresh');

      // Act
      component['setFilterValue']();

      // Assert
      expect(component['softWareBuildPageRefresh']).not.toHaveBeenCalled();
    });

    it('should not set values when softwareBuildSearchRequestBody is null', () => {
      // Setup
      component.softwareBuildSearchRequestBody = null;
      component.listPageRefreshForbackToDetailPage = false;
      const initialItemNumber = component.filterForm.get('itemNumber').value;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('itemNumber').value).toBe(initialItemNumber);
    });

    it('should handle jsonIds filtering correctly', () => {
      // Setup
      const searchRequestWithJsonIds = new SoftwareBuildSearchRequestBody(
        'test-version', [1, 2], deviceTypesEnum.CLIENT_DEVICE, true, [1, 3], 'test-part'
      );
      component.softwareBuildSearchRequestBody = searchRequestWithJsonIds;
      component.countryList = mockCountryList;
      component.jsonVersionList = [
        new Jsonlist(1, '1.0.0'),
        new Jsonlist(2, '2.0.0'),
        new Jsonlist(3, '3.0.0')
      ];

      // Act
      component['setFilterValue']();

      // Assert
      const expectedJsonVersions = component.jsonVersionList.filter(json => [1, 3].includes(json.id));
      expect(component.filterForm.get('jsonVersions').value).toEqual(expectedJsonVersions);
    });

    it('should handle inactive status correctly', () => {
      // Setup
      const searchRequestWithInactiveStatus = new SoftwareBuildSearchRequestBody(
        'test-version', [1, 2], deviceTypesEnum.CLIENT_DEVICE, false, [1], 'test-part'
      );
      component.softwareBuildSearchRequestBody = searchRequestWithInactiveStatus;
      component.countryList = mockCountryList;
      component.jsonVersionList = mockJsonVersionList;

      // Act
      component['setFilterValue']();

      // Assert
      expect(component.filterForm.get('inventoryStatus').value).toEqual([SoftwareBuildStatusEnum.INACTIVE]);
    });
  });

  describe('Component Properties and Constants', () => {
    it('should have correct constant values', () => {
      expect(component.textBoxMaxLength).toBe(50); // SMALL_TEXTBOX_MAX_LENGTH
      expect(component.textBoxMaxCharactersAllowedMessage).toBe('Maximum 50 characters allowed');
      expect(component.specialCharacterErrorMessage).toBe('Single quotes are not allowed.');
      expect(component.version).toBe('Version');
      expect(component.partNumber).toBe('Part Number');
      expect(component.country).toBe('Country');
      expect(component.deviceType).toBe('Device Type');
      expect(component.status).toBe('Status');
      expect(component.jsonVersion).toBe('Json Version');
      expect(component.search).toBe('Search');
      expect(component.clear).toBe('Clear');
    });

    it('should initialize arrays correctly', () => {
      expect(component.countryList).toEqual([]);
      expect(component.jsonVersionList).toEqual([]);
      expect(component.userAssociatedCountrys).toEqual([]);
    });

    it('should have correct default ListingPageReloadSubjectParameter', () => {
      expect(component.defaultListingPageReloadSubjectParameter.isReloadData).toBe(true);
      expect(component.defaultListingPageReloadSubjectParameter.isDefaultPageNumber).toBe(true);
      expect(component.defaultListingPageReloadSubjectParameter.isClearFilter).toBe(false);
      expect(component.defaultListingPageReloadSubjectParameter.isOtherAction).toBe(false);
    });
  });

  describe('Form Validation', () => {
    it('should have correct form structure', () => {
      expect(component.filterForm.get('itemNumber')).toBeDefined();
      expect(component.filterForm.get('deviceType')).toBeDefined();
      expect(component.filterForm.get('country')).toBeDefined();
      expect(component.filterForm.get('jsonVersions')).toBeDefined();
      expect(component.filterForm.get('inventoryStatus')).toBeDefined();
      expect(component.filterForm.get('partNumber')).toBeDefined();
    });

    it('should validate itemNumber field correctly', () => {
      const itemNumberControl = component.filterForm.get('itemNumber');

      // Test valid input
      itemNumberControl.setValue('valid-input');
      expect(itemNumberControl.valid).toBe(true);

      // Test invalid input with single quote
      itemNumberControl.setValue("invalid'input");
      expect(itemNumberControl.valid).toBe(false);

      // Test max length validation
      itemNumberControl.setValue('a'.repeat(51));
      expect(itemNumberControl.valid).toBe(false);
    });

    it('should validate partNumber field correctly', () => {
      const partNumberControl = component.filterForm.get('partNumber');

      // Test valid input
      partNumberControl.setValue('valid-part');
      expect(partNumberControl.valid).toBe(true);

      // Test invalid input with single quote
      partNumberControl.setValue("invalid'part");
      expect(partNumberControl.valid).toBe(false);

      // Test max length validation
      partNumberControl.setValue('a'.repeat(51));
      expect(partNumberControl.valid).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle null response from video service', () => {
      // Setup
      const mockResponse = new HttpResponse({ body: null });
      videoService.getListofJsonVersions.and.returnValue(of(mockResponse));
      const commonsService = TestBed.inject(CommonsService);
      spyOn(commonsService, 'checkForNull').and.returnValue([]);

      // Act
      component['getJsonVersion']();

      // Assert
      expect(commonsService.checkForNull).toHaveBeenCalledWith(null);
      expect(component.jsonVersionList).toEqual([]);
    });

    it('should handle undefined subscription in ngOnDestroy', () => {
      // Setup
      component.subscriptionForRefreshList = undefined;

      // Act & Assert
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });
});
