export enum PermissionAction {
    //Device
    GET_DEVICE_ACTION = "getDevice",
    UPDATE_DEVICE_TYPE_ACTION = "updateDeviceType",
    LOCK_DEVICE_ACTION = "locakDevice",
    ASSOCIATE_CUSTOMER_TO_DEVICE_ACTION = "associateCustomerToDevice",
    DISABLE_DEVICE_ACTION = "disableDevice",
    RMA_DEVICE_ACTION = "rmaDevice",
    EDITABLE_DEVICE_ACTION = "editableDevice",
    TRANSFER_DEVICE_ACTION = "transferDevice",

    //Prob
    GET_PROB_ACTION = "getProb",
    ADD_PROB_ACTION = "addProb",
    DELETE_PROB_ACTION = "deleteProb",
    DOWNLOAD_PROB_FEATURE_LICENSE_ACTION = "downloadProbFeatureLicense",
    DOWNLOAD_SALESORDER_LETTER_ACTION = "downloadSalesOrderAction",
    ADD_PROBE_SET_REMINDER_OPTIONS_DISPLAY = "addProbeSetReminderOptionsDisplay",
    DISABLE_PROBE_ACTION = "disableProbe",
    EDITABLE_PROBE_ACTION = "editableProbe",
    RMA_PROBE_ACTION = "rmaProbe",
    UPDATE_PROB_TYPE_ACTION = "updateProbType",
    ASSOCIATE_CUSTOMER_TO_PROBE_ACTION = "associateCustomerToProbe",
    ASSIGN_FEATURE_TO_PROBE_ACTION = "assignFeatureToProbe",
    TRANSFER_PROBE_ACTION = "probeTransfer",

    //Item Inventory
    GET_SOFTWARE_BUILD_ACTION = "getSoftwareBuild",
    DELETE_SOFTWARE_BUILD_ACTION = "deleteSoftwareBuild",
    UPDATE_SOFTWARE_BUILD_ACTION = "updateSoftwareBuild",
    UPLOAD_SOFTWARE_BUILD_ACTION = "uploadSoftwareBuild",

    //Role
    GET_ROLE_ACTION = "getRole",
    GET_ROLE_NAME_LIST_ACTION = "getRoleNameListAction",
    ADD_ROLE_ACTION = "addRole",
    UPDATE_ROLE_ACTION = "updateRole",
    DELETE_ROLE_ACTION = "deleteRole",

    //USER
    GET_USER_ACTION = "getUser",
    ADD_USER_ACTION = "addUser",
    UPDATE_USER_ACTION = "updateUser",
    DELETE_USER_ACTION = "deleteUser",

    //video
    GET_VIDEO_ACTION = "getVideo",
    ADD_VIDEO_ACTION = "addVideo",
    UPDATE_VIDEO_ACTION = "updateVideo",
    DELETE_VIDEO_ACTION = "deleteVideo",

    //Device Log
    GET_DEVICE_LOG_ACTION = "getDeviceLog",
    DOWNLOAD_DEVICE_LOG_ACTION = "downloadDeviceLog",

    //JOB
    GET_JOB_ACTION = "getJob",

    //Country
    GET_COUNTRY_LIST_ACTION = "getCountryList",
    ADD_COUNTRY_ACTION = "addCountry",
    DELETE_COUNTRY_ACTION = "deleteCountry",

    //Get Permission
    GET_PERMISSIONLIST_ACTION = "getPermission",

    //Kit management
    KIT_MANAGEMANT_TAB_ACTION = "kitManagementTab",

    //Brige Kit Management
    GET_BRIDGE_KIT_MANAGEMENT_ACTION = "getKitManagement",
    IMPORT_BRIDGE_KIT_CSV_ACTION = "importKitCSV",

    //OTSKit Management
    GET_OTS_KIT_MANAGEMENT_ACTION = "getOTSKitManagement",
    IMPORT_OTS_KIT_CSV_ACTION = "importOtsKitCSV",

    //Language 
    GET_LANGUAGE_ACTION = "getLanguages",

    //Sales order
    GET_SALES_ORDER_ACTION = "getSalesOrder",
    SALES_ORDER_CREATE_PROBE_ACTION = "salesOrderCreateProbe",
    SALES_ORDER_RESET_BRIDGE_ACTION = "salesOrderResetBridge",
    SALES_ORDER_DELETE_ACTION = "deleteSalesOrder",
    SALES_ORDER_MANUAL_SYNC_ACTION = "salesOrderManualSync",
    SALES_ORDER_TRANSFER_ORDER_ACTION = "salesOrderTransferProduct",

    //Audit
    GET_AUDIT_ACTION = "getAudit",
    AUDIT_PROBE_REVERSE_ACTION = "auditReverseActionForProbe",
    AUDIT_DEVICE_REVERSE_ACTION = "auditReverseActionForDevice",

    //Probr Config Group
    GET_CONFIG_GROUP_ACTION = "getProbeConfigGroup",
    ADD_CONFIG_GROUP_ACTION = "addProbeConfigGroup",
    DELETE_CONFIG_GROUP_ACTION = "deleteProbeConfigGroup",

    //Connection History
    GET_CONNECTION_HISTORY_ACTION = "getConnectionHistory"

}