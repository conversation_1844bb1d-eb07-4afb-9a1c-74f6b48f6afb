import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

import { SoftwareBuildConfirmComponent } from './software-build-confirm.component';

describe('SoftwareBuildConfirmComponent', () => {
  let component: SoftwareBuildConfirmComponent;
  let fixture: ComponentFixture<SoftwareBuildConfirmComponent>;
  let activeModalSpy: jasmine.SpyObj<NgbActiveModal>;

  beforeEach(async () => {
    activeModalSpy = jasmine.createSpyObj('NgbActiveModal', ['close']);

    await TestBed.configureTestingModule({
      declarations: [SoftwareBuildConfirmComponent],
      providers: [
        { provide: NgbActiveModal, useValue: activeModalSpy }
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildConfirmComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ==================== INPUT PROPERTIES TESTS ====================

  describe('Input Properties', () => {
    it('should accept btnOkText input', () => {
      const testValue = 'Confirm';
      component.btnOkText = testValue;
      expect(component.btnOkText).toBe(testValue);
    });

    it('should accept btnCancelText input', () => {
      const testValue = 'Cancel';
      component.btnCancelText = testValue;
      expect(component.btnCancelText).toBe(testValue);
    });

    it('should accept header input', () => {
      const testValue = 'Confirmation Dialog';
      component.header = testValue;
      expect(component.header).toBe(testValue);
    });

    it('should accept mainbody input', () => {
      const testValue = 'Are you sure you want to proceed?';
      component.mainbody = testValue;
      expect(component.mainbody).toBe(testValue);
    });

    it('should accept activeModelReference input', () => {
      const testValue = 'test-reference';
      component.activeModelReference = testValue;
      expect(component.activeModelReference).toBe(testValue);
    });
  });

  // ==================== MODAL ACTIONS TESTS ====================

  describe('Modal Actions', () => {
    it('should close modal with false when decline is called', () => {
      component.decline();
      expect(activeModalSpy.close).toHaveBeenCalledWith(false);
    });

    it('should close modal with true when accept is called', () => {
      component.accept();
      expect(activeModalSpy.close).toHaveBeenCalledWith(true);
    });

    it('should close modal with false when close is called', () => {
      component.close();
      expect(activeModalSpy.close).toHaveBeenCalledWith(false);
    });
  });

  // ==================== COMPONENT INITIALIZATION TESTS ====================

  describe('Component Initialization', () => {
    it('should initialize with undefined input properties', () => {
      expect(component.btnOkText).toBeUndefined();
      expect(component.btnCancelText).toBeUndefined();
      expect(component.header).toBeUndefined();
      expect(component.mainbody).toBeUndefined();
      expect(component.activeModelReference).toBeUndefined();
    });

    it('should have NgbActiveModal injected', () => {
      expect(component['activeModal']).toBeDefined();
      expect(component['activeModal']).toBe(activeModalSpy);
    });
  });
});
