import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subject, firstValueFrom, lastValueFrom } from 'rxjs';
import {
  COMMON_SELECT_FILTER,
  HISTORICAL_DATA_FILTER_APPLY_MESSAGE,
  PERMISSION_ERROR_MESSAGE,
  PROBE_ALREADY_EDIT_DISABLE,
  PROBE_ALREADY_EDIT_ENABLE,
  PROBE_ALREADY_LOCKED,
  PROBE_ALREADY_UNLOCKED,
  ProbDetailResource,
  ProbListResource,
  Probe_Select_Message,
  Probe_Single_Select_Message,
  Update_Probe_CancelButton,
  Update_Probe_OkButton,
  Update_Probe_title
} from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { CustomerAssociationModelRequest } from 'src/app/model/customer-association-model/customer-association-model-request.model';
import { CustomerAssociationRequest } from 'src/app/model/customer-association-request';
import { PresetDetailBaseResponse } from 'src/app/model/Presets/PresetDetailBaseResponse.model';
import { ConfigureLicenceDetails } from 'src/app/model/probe/ConfigureLicenceDetails.model';
import { ConfigureLicenceResponse } from 'src/app/model/probe/ConfigureLicenceResponse.model';
import { FeaturesFilter } from 'src/app/model/probe/FeaturesFilter.model';
import { ProbeDetailResponse } from 'src/app/model/probe/probeDetail.model';
import { ProbeDownloadCSVParameterRequest } from 'src/app/model/probe/ProbeDownloadCSVParameterRequest.model';
import { ProbeDownloadCSVRequest } from 'src/app/model/probe/ProbeDownloadCSVRequest.model';
import { ProbeFilterAction } from 'src/app/model/probe/ProbeFilterAction.model';
import { ProbeListFilterRequestBody } from 'src/app/model/probe/ProbeListFilterRequestBody.model';
import { ProbeSearchParameterRequest } from 'src/app/model/probe/ProbeSearchParameterRequest.model';
import { DeviceService } from 'src/app/shared/device.service';
import { ProbeOperationsEnum } from 'src/app/shared/enum/Operations/ProbeOperations.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { DeviceHistoricalData } from 'src/app/shared/enum/Probe/DeviceHistoricalData.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CustomerAssociationService } from 'src/app/shared/modalservice/customer-association.service';
import { UpdateProbeTypeService } from 'src/app/shared/modalservice/Probe/update-probe-type.service';
import { UpdateFeaturesService } from 'src/app/shared/modalservice/update-features.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeService } from 'src/app/shared/Service/ProbeService/probe.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';

/**
* Probe List Result interface for consistent response handling
*/
export interface ProbeListResult {
  success: boolean;
  probes: ProbeDetailResponse[];
  totalProbeDisplay: number;
  totalProbes: number;
  localProbeList: ProbeDetailResponse[];
  totalItems: number;
  page: number;
}

/**
* Probe Operation Service for communication between probe filter and listing components
* Includes caching functionality to avoid unnecessary API calls on filter show/hide
*
* <AUTHOR>
*/
@Injectable({
  providedIn: 'root'
})
export class ProbeOperationService {

  constructor(
    private probeApiService: ProbeApiService,
    private probeService: ProbeService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private countryCacheService: CountryCacheService,
    private commonsService: CommonsService,
    private exceptionHandlingService: ExceptionHandlingService,
    private toastrService: ToastrService,
    private downloadService: DownloadService,
    private moduleValidationService: ModuleValidationServiceService,
    private permissionService: PermissionService,
    private confirmDialogService: ConfirmDialogService,
    private updateFeaturesService: UpdateFeaturesService,
    private customerAssociationService: CustomerAssociationService,
    private updateProbeTypeService: UpdateProbeTypeService,
    private deviceService: DeviceService
  ) { }

  /**
  * Cached filter data to avoid API calls on filter show/hide
  * <AUTHOR>
  */
  private salesOrderNumberList: string[] = [];
  private probeTypesList: string[] = [];
  private featuresList: FeaturesFilter[] = [];
  private presetList: PresetDetailBaseResponse[] = [];
  private countryList: CountryListResponse[] = [];
  private deviceHistoricalData: DeviceHistoricalData = DeviceHistoricalData.NORMAL;

  //Loading Status
  private probeListLoadingSubject = new Subject<boolean>();
  private probeDetailLoadingSubject = new Subject<boolean>();

  //Refresh Probe List
  private probeListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Refresh Probe Detail page
  private probeDetailRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Probe list filter
  private probeListFilterRequestParameterSubject = new Subject<ProbeFilterAction>();

  //Probe Listing Page State Manage
  probeSearchRequestBody: ProbeListFilterRequestBody = null;
  isFilterHidden: boolean = false;
  listPageRefreshForbackToOtherPage: boolean = false;

  /**
  * Probe List Page Loading
  * <AUTHOR>
  * @returns Subject<boolean>
  */
  public getProbeListLoadingSubject(): Subject<boolean> {
    return this.probeListLoadingSubject;
  }

  public callProbeListLoadingSubject(status: boolean): void {
    this.probeListLoadingSubject.next(status);
  }

  /**
  * Probe Detail Page Loading
  * <AUTHOR>
  * @returns Subject<boolean>
  */
  public getProbeDetailLoadingSubject(): Subject<boolean> {
    return this.probeDetailLoadingSubject;
  }

  public callProbeDetailLoadingSubject(status: boolean): void {
    this.probeDetailLoadingSubject.next(status);
  }

  /**
  * Probe List Page Refresh After some Action Like Create or Update or Delete Probe
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getProbeListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.probeListRefreshSubject;
  }

  /**
  * Probe Detail Page Refresh After some Action Like Create or Update or Delete Probe
  * isReloadData false means delete Probe operation and move list page
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getProbeDetailRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.probeDetailRefreshSubject;
  }

  /**
  * Get Probe List Filter Request Parameter Subject
  * Used by probe listing component to subscribe to filter changes
  * <AUTHOR>
  * @returns Subject<ProbeFilterAction>
  */
  public getProbeListFilterRequestParameterSubject(): Subject<ProbeFilterAction> {
    return this.probeListFilterRequestParameterSubject;
  }

  /**
  * Set Device List Search Request Body
  * 
  * <AUTHOR> 
  * @param probeSearchRequestBody 
  */
  public setProbeSearchRequestBodyForListingApi(probeSearchRequestBody: ProbeListFilterRequestBody) {
    this.probeSearchRequestBody = probeSearchRequestBody;
  }

  /**
  * Get Device List Search Request Body
  * 
  * <AUTHOR>
  * @returns 
  */
  public getProbeSearchRequestBodyForListingApi(): ProbeListFilterRequestBody {
    return this.probeSearchRequestBody;
  }

  /**
  * Set Filter Hide/Show
  * <AUTHOR>
  * @param isFilterHidden 
  */
  public setIsFilterHiddenForListing(isFilterHidden: boolean) {
    this.isFilterHidden = isFilterHidden;
  }

  /**
  * Get Filter Hide/Show
  * <AUTHOR>
  * @param isFilterHidden 
  */
  public getIsFilterHiddenForListing(): boolean {
    return this.isFilterHidden;
  }
  /**
   * set listPageRefreshForbackToOtherPage
   * <AUTHOR>
   * @param listPageRefreshForbackToOtherPage 
   */
  public setListPageRefreshForbackToOtherPage(listPageRefreshForbackToOtherPage: boolean) {
    this.listPageRefreshForbackToOtherPage = listPageRefreshForbackToOtherPage;
  }

  /**
  * get listPageRefreshForbackToOtherPage
  * @auther 112038
  * @returns 
  */
  public getListPageRefreshForbackToOtherPage(): boolean {
    return this.listPageRefreshForbackToOtherPage;
  }

  /**
  * Call Probe List Filter Request Parameter Subject
  * Used by probe filter component to emit filter changes
  * <AUTHOR>
  * @param probeFilterAction - The filter action containing search parameters
  */
  public callProbeListFilterRequestParameterSubject(probeFilterAction: ProbeFilterAction): void {
    this.probeListFilterRequestParameterSubject.next(probeFilterAction);
  }

  /**
  * This function call the subject for loading start and stop
  * <AUTHOR>
  * @param status
  * @param resourceName
  */
  public isLoading(status: boolean, resourceName: string): void {
    if (resourceName == ProbListResource) {
      this.callProbeListLoadingSubject(status);
    } else if (resourceName == ProbDetailResource) {
      this.callProbeDetailLoadingSubject(status);
    }
  }

  /**
  * This function call the subject for reload the page data
  * Note : (ProbListResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter
  * @param resourceName
  * @param isFilterHidden
  * @param probeListFilterRequestBodyApply
  */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean, probeListFilterRequestBodyApply: ProbeListFilterRequestBody): void {
    if (resourceName == ProbListResource) {
      if (isFilterHidden) {
        // Always use filter subject for list refresh (same pattern as device service)
        let probeListFilterRequestBody = new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null);
        if (!isNullOrUndefined(probeListFilterRequestBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
          probeListFilterRequestBody = probeListFilterRequestBodyApply;
        }
        let probeFilterAction = new ProbeFilterAction(listingPageReloadSubjectParameter, probeListFilterRequestBody);
        this.callProbeListFilterRequestParameterSubject(probeFilterAction);
      } else {
        this.probeListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    } else if (resourceName == ProbDetailResource) {
      this.probeDetailRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }

  /**
   * Call filter page subject for reload page
   * Used to maintain filter state when navigating between listing and detail pages
   * <AUTHOR>
   * @param isDefaultPageNumber - Whether to reset to default page number
   * @param isClearFilter - Whether to clear filters
   */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    this.callRefreshPageSubject(listingPageReloadSubjectParameter, ProbListResource, false, null);
  }

  /**
  * Cache management methods
  */

  /**
  * Get Sales Order Number List from cache (synchronous)
  * <AUTHOR>
  * @returns Cached array of sales order numbers
  */
  public getSalesOrderNumberListFromCache(): string[] {
    return this.salesOrderNumberList;
  }

  /**
  * Get Country List from cache (synchronous)
  * <AUTHOR>
  * @returns Cached array of countries
  */
  public getCountryListFromCache(): CountryListResponse[] {
    return this.countryList;
  }

  /**
  * Get Probe Types List from cache (synchronous)
  * <AUTHOR>
  * @returns Cached array of probe types
  */
  public getProbeTypesListFromCache(): string[] {
    return this.probeTypesList;
  }

  /**
  * Get Features List from cache (synchronous)
  * <AUTHOR>
  * @returns Cached array of features
  */
  public getFeaturesListFromCache(): FeaturesFilter[] {
    return this.featuresList;
  }

  /**
  * Get Preset List from cache (synchronous)
  * <AUTHOR>
  * @returns Cached array of presets
  */
  public getPresetListFromCache(): PresetDetailBaseResponse[] {
    return this.presetList;
  }

  /**
  * Set Sales Order Number List
  * <AUTHOR>
  * @param salesOrderNumberList - Array of sales order numbers to cache
  */
  public setSalesOrderNumberList(salesOrderNumberList: string[]): void {
    this.salesOrderNumberList = salesOrderNumberList;
  }

  /**
  * Get Sales Order Number List
  * Returns cached data if available, otherwise makes API call
  * <AUTHOR>
  * @returns Promise<string[]>
  */
  public async getSalesOrderNumberList(): Promise<string[]> {
    if (this.salesOrderNumberList.length === 0) {
      this.salesOrderNumberList = await this.getSalesOrderNumbersFromAPI();
    }
    return this.salesOrderNumberList;
  }

  /**
  * Set Probe Types List
  * <AUTHOR>
  * @param probeTypesList - Array of probe types to cache
  */
  public setProbeTypesList(probeTypesList: string[]): void {
    this.probeTypesList = probeTypesList;
  }

  /**
  * Get Probe Types List
  * Returns cached data if available, otherwise makes API call
  * <AUTHOR>
  * @returns Promise<string[]>
  */
  public async getProbeTypesList(): Promise<string[]> {
    if (this.probeTypesList.length === 0) {
      this.probeTypesList = await this.getProbeTypesFromAPI();
    }
    return this.probeTypesList;
  }

  /**
  * Set Features List
  * <AUTHOR>
  * @param featuresList - Array of features to cache
  */
  public setFeaturesList(featuresList: FeaturesFilter[]): void {
    this.featuresList = featuresList;
  }

  /**
  * Get Features List
  * Returns cached data if available, otherwise makes API call
  * <AUTHOR>
  * @returns Promise<FeaturesFilter[]>
  */
  public async getFeaturesList(): Promise<FeaturesFilter[]> {
    if (this.featuresList.length === 0) {
      this.featuresList = await this.getFeaturesFromAPI();
    }
    return this.featuresList;
  }

  /**
  * Set Preset List
  * <AUTHOR>
  * @param presetList - Array of presets to cache
  */
  public setPresetList(presetList: PresetDetailBaseResponse[]): void {
    this.presetList = presetList;
  }

  /**
  * Get Preset List
  * Returns cached data if available, otherwise makes API call
  * <AUTHOR>
  * @returns Promise<PresetDetailBaseResponse[]>
  */
  public async getPresetList(): Promise<PresetDetailBaseResponse[]> {
    if (this.presetList.length === 0) {
      this.presetList = await this.getPresetsFromAPI();
    }
    return this.presetList;
  }

  /**
  * Set Country List
  * <AUTHOR>
  * @param countryList - Array of countries to cache
  */
  public setCountryList(countryList: CountryListResponse[]): void {
    this.countryList = countryList;
  }

  /**
  * Get Country List
  * Returns cached data if available, otherwise makes API call
  * <AUTHOR>
  * @returns Promise<CountryListResponse[]>
  */
  public async getCountryList(): Promise<CountryListResponse[]> {
    if (this.countryList.length === 0) {
      this.countryList = await this.getCountriesFromAPI();
    }
    return this.countryList;
  }

  /**
  * Update cache in background by making fresh API calls
  * This method is called when filter is hidden to keep cache fresh
  * <AUTHOR>
  */
  public async updateCacheInBackground(): Promise<void> {
    // Make API calls in parallel and update cache
    const [salesOrderNumbers, probeTypes, features, presets, countries] = await Promise.all([
      this.getSalesOrderNumbersFromAPI(),
      this.getProbeTypesFromAPI(),
      this.getFeaturesFromAPI(),
      this.getPresetsFromAPI(),
      this.getCountriesFromAPI()
    ]);

    // Update cache with fresh data
    this.salesOrderNumberList = salesOrderNumbers;
    this.probeTypesList = probeTypes;
    this.featuresList = features;
    this.presetList = presets;
    this.countryList = countries;
  }

  /**
  * Private API methods for fetching fresh data
  */

  /**
  * Get Sales Order Numbers from API
  * <AUTHOR>
  * @returns Promise<string[]>
  */
  private async getSalesOrderNumbersFromAPI(): Promise<string[]> {
    try {
      return await this.salesOrderApiCallService.getSalesOrderNumberList();
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return [];
    }
  }

  /**
  * Get Probe Types from API
  * <AUTHOR>
  * @returns Promise<string[]>
  */
  private async getProbeTypesFromAPI(): Promise<string[]> {
    try {
      const response = await firstValueFrom(this.probeApiService.getProbeTypesList());
      return response.body || [];
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return [];
    }
  }

  /**
  * Get Features from API
  * <AUTHOR>
  * @returns Promise<FeaturesFilter[]>
  */
  private async getFeaturesFromAPI(): Promise<FeaturesFilter[]> {
    try {
      const featuresResponse = await this.probeApiService.getFeaturesList();
      return this.probeService.getFeaturesListForFilter(featuresResponse, false);
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return [];
    }
  }

  /**
  * Get Presets from API
  * <AUTHOR>
  * @returns Promise<PresetDetailBaseResponse[]>
  */
  private async getPresetsFromAPI(): Promise<PresetDetailBaseResponse[]> {
    try {
      const presetsResponse = await this.probeApiService.getPresetsList();
      return this.probeService.getPresetsListForFilter(presetsResponse, false);
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return [];
    }
  }

  /**
  * Get Countries from API
  * <AUTHOR>
  * @returns Promise<CountryListResponse[]>
  */
  private async getCountriesFromAPI(): Promise<CountryListResponse[]> {
    try {
      return await this.countryCacheService.getCountryListFromCache(true);
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return [];
    }
  }

  /**
  * Load probe list with search parameters and pagination
  * Handles API call, response processing, and error handling
  * <AUTHOR>
  * @param probeListFilterRequestBody - Search criteria for filtering probes
  * @param pageObj - Pagination parameters (page, size)
  * @param deviceHistoricalData - Historical data parameter
  * @returns Promise with probe list result
  */
  public async loadProbeList(probeListFilterRequestBody: ProbeListFilterRequestBody, pageObj: any, deviceHistoricalData: string): Promise<ProbeListResult> {
    try {
      debugger
      if (this.permissionService.getProbPermission(PermissionAction.GET_PROB_ACTION)) {
        const reqParameter = new ProbeSearchParameterRequest(pageObj.page, pageObj.size, deviceHistoricalData);
        const response = await firstValueFrom(this.probeApiService.getAllProbes(probeListFilterRequestBody, reqParameter));

        if (response.status === 200 && response.body) {
          const probeData = response.body;
          return {
            success: true,
            probes: probeData.content,
            totalProbeDisplay: probeData.numberOfElements,
            totalProbes: probeData.totalElements,
            localProbeList: probeData.content,
            totalItems: probeData.totalElements,
            page: probeData.number + 1
          };
        } else {
          return this.getEmptyProbeListResult();
        }
      } else {
        this.toastrService.error('Insufficient permissions to load probe list');
        return this.getEmptyProbeListResult();
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return this.getEmptyProbeListResult();
    }
  }

  /**
  * Get empty probe list result for error cases
  * <AUTHOR>
  * @returns ProbeListResult
  */
  private getEmptyProbeListResult(): ProbeListResult {
    return {
      success: false,
      probes: [],
      totalProbeDisplay: 0,
      totalProbes: 0,
      localProbeList: [],
      totalItems: 0,
      page: 0
    };
  }

  /**
  * Process filter search and validate form
  * <AUTHOR>
  * @param formValue - Form values from filter component
  * @param isFormInvalid - Whether form is invalid
  * @param defaultListingPageReloadSubjectParameter - Default reload parameters
  * @returns boolean indicating if search should proceed
  */
  public processFilterSearch(formValue: any, isFormInvalid: boolean, defaultListingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): boolean {
    if (isFormInvalid) {
      return false;
    }

    if (!this.validateProbeFilterForm(formValue)) {
      return false;
    }

    const probeListFilterRequestBody = this.buildProbeListFilterRequestBody(formValue);
    const probeFilterAction = new ProbeFilterAction(defaultListingPageReloadSubjectParameter, probeListFilterRequestBody);
    this.callProbeListFilterRequestParameterSubject(probeFilterAction);
    return true;
  }

  /**
  * Validate probe filter form
  * <AUTHOR>
  * @param formValue - Form values to validate
  * @returns boolean indicating if validation passed
  */
  public validateProbeFilterForm(formValue: any): boolean {
    const {
      serialNumber, probeTypes, probeFeatures, presetType, customerName, salesOrderNumber,
      deviceModel, manufacturer, featureValidityPeriod, osType, productStatus,
      deviceHistoricalData, countries, lockState, probeEditState
    } = formValue;

    const hasAnyFilter = serialNumber || probeTypes?.length || probeFeatures?.length ||
      presetType?.length || customerName || salesOrderNumber?.length ||
      deviceModel || manufacturer || featureValidityPeriod?.length ||
      osType?.length || productStatus?.length || deviceHistoricalData ||
      countries?.length || lockState?.length || probeEditState?.length;

    if (!hasAnyFilter) {
      this.toastrService.info(COMMON_SELECT_FILTER);
      return false;
    }

    if (deviceHistoricalData && !deviceModel && !manufacturer && !osType?.length) {
      this.toastrService.info(HISTORICAL_DATA_FILTER_APPLY_MESSAGE);
      return false;
    }

    return true;
  }

  /**
  * Build probe list filter request body from form values
  * <AUTHOR>
  * @param formValue - Form values from filter component
  * @returns ProbeListFilterRequestBody
  */
  public buildProbeListFilterRequestBody(formValue: any): ProbeListFilterRequestBody {
    const serialNumber = this.commonsService.checkNullFieldValue(formValue.serialNumber);
    const customerName = this.commonsService.checkNullFieldValue(formValue.customerName);
    const deviceModel = this.commonsService.checkNullFieldValue(formValue.deviceModel);
    const manufacturer = this.commonsService.checkNullFieldValue(formValue.manufacturer);

    const countryIds = isNullOrUndefined(formValue.countries) ? null : this.commonsService.getIdsFromArray(formValue.countries);
    const probeFeatures = isNullOrUndefined(formValue.probeFeatures) ? null : this.getFeatureIds(formValue.probeFeatures);
    const presetType = this.commonsService.getIdsFromArray(formValue.presetType);
    const validityPeriod = this.probeApiService.getFilterValue(formValue.featureValidityPeriod, true);
    const productStatus = this.commonsService.getSelectedValueFromEnum(formValue.productStatus);
    const osType = this.commonsService.getSelectedValueFromEnum(formValue.osType);
    const locked = this.commonsService.getSelectedValueFromBooleanKeyValueMapping(formValue.lockState);
    const probeEditState = this.commonsService.getSelectedValueFromBooleanKeyValueMapping(formValue.probeEditState);

    return new ProbeListFilterRequestBody(
      this.probeApiService.getFilterValue(serialNumber, false),
      this.probeApiService.getFilterValue(formValue.probeTypes, true),
      this.probeApiService.getFilterValue(probeFeatures, true),
      this.probeApiService.getFilterValue(presetType, true),
      this.probeApiService.getFilterValue(customerName, false),
      this.probeApiService.getFilterValue(formValue.salesOrderNumber, true),
      this.probeApiService.getFilterValue(deviceModel, false),
      this.probeApiService.getFilterValue(manufacturer, false),
      osType.length === 1 ? osType[0] : null,
      validityPeriod?.length === 1 ? validityPeriod[0].value : null,
      productStatus,
      countryIds,
      locked,
      probeEditState
    );
  }

  /**
  * Extract feature IDs from feature objects
  * <AUTHOR>
  * @param featuresList - Array of feature objects
  * @returns Array of feature IDs
  */
  private getFeatureIds(featuresList: any[]): number[] {
    if (!featuresList || featuresList.length === 0) {
      return null;
    }

    const idList = [];
    for (const featureObj of featuresList) {
      idList.push(featureObj.id);
    }
    return idList;
  }

  /**
  * Clear all filters and refresh the listing
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter - Reload parameters
  */
  public clearAllFiltersAndRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    const emptyFilterRequestBody = new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null);
    const probeFilterAction = new ProbeFilterAction(listingPageReloadSubjectParameter, emptyFilterRequestBody);
    this.callProbeListFilterRequestParameterSubject(probeFilterAction);
  }

  // ==================== PROBE OPERATION METHODS ====================

  /**
  * Show success message and refresh page based on resource type
  * <AUTHOR>
  * @param message - Success message to display
  * @param resourceName - Resource name for determining refresh action
  */
  private showSuccessAndRefresh(resourceName: string): void {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    this.callRefreshPageSubject(listingPageReloadSubjectParameter, resourceName, false, null);
  }

  /**
 * Probe Operation Action
 * <AUTHOR>
 * @param operationName
 * @param resourceName
 * @param selectedProbeIdList
 * @param selectedProbeList
 */
  public changeOperationForProbe(operationName: string, resourceName: string, selectedProbeIdList: number[], selectedProbeList: any[]): void {//NOSONAR
    if (selectedProbeIdList.length == 0 && operationName !== ProbeOperationsEnum.Export_CSV) {
      this.toastrService.info(Probe_Select_Message);
    } else {
      switch (operationName) {
        case ProbeOperationsEnum.UNLOCK_PROBES:
          this.lockUnlockProbes(selectedProbeIdList, selectedProbeList, false, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.LOCK_PROBES:
          this.lockUnlockProbes(selectedProbeIdList, selectedProbeList, true, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.EDIT_ENABLE_PROBE:
          this.enableDisableProbes(selectedProbeIdList, selectedProbeList, true, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) })
          break;
        case ProbeOperationsEnum.EDIT_DISABLE_PROBE:
          this.enableDisableProbes(selectedProbeIdList, selectedProbeList, false, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.DELETE_PROBES:
          this.deleteProbes(selectedProbeIdList, selectedProbeList, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.RMA_PROBE:
          this.rmaProductStatusForProbes(selectedProbeIdList, selectedProbeList, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.DISABLED_PROBE:
          this.disableProductStatusForProbes(selectedProbeIdList, selectedProbeList, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.CUSTOMER_ASSOCIATION:
          this.associationProbeWithSalesOrder(selectedProbeIdList, selectedProbeList, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.ASSIGN_FEATURES_TO_PROBE:
          this.updateProbeFeatures(selectedProbeIdList, selectedProbeList, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.DOWNLOAD_PROBES:
          this.downloadProbes(selectedProbeIdList, selectedProbeList, resourceName);
          break;
        case ProbeOperationsEnum.UPDATE_PROBE_TYPE:
          // For detail resource, pass current probe type; for list resource, pass null
          const currentProbeType = (resourceName === ProbDetailResource && selectedProbeList.length > 0)
            ? selectedProbeList[0].type
            : null;
          this.updateProbeType(selectedProbeIdList, selectedProbeList, currentProbeType, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.Export_CSV:
          this.exportProbesCSV(selectedProbeIdList, selectedProbeList, resourceName).then(res => { if (res) this.showSuccessAndRefresh(resourceName) });
          break;
        case ProbeOperationsEnum.TRANSFER_PROBE:
          // This operation is only available for detail resource with single probe
          if (resourceName === ProbDetailResource && selectedProbeList.length > 0) {
            this.transferProbes([selectedProbeIdList[0]], [selectedProbeList[0]], resourceName);
          }
          break;
        default:
          break;
      }
    }
  }

  /**
  * Lock or unlock probes
  * Handles validation and API call for probe state change
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to lock/unlock
  * @param selectedProbes - Array of selected probe objects for validation
  * @param lockState - true to lock, false to unlock
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<void> indicating operation success
  */
  public async lockUnlockProbes(probeIds: number[], selectedProbes: any[], lockState: boolean, resourceName: string): Promise<boolean> {
    try {
      // Perform validation based on resource type
      if (!this.validateProbeLockUnlockPermissions(probeIds, selectedProbes, lockState, resourceName)) {
        return false;
      }

      if (!this.permissionService.getProbPermission(PermissionAction.LOCK_DEVICE_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE)
        return false;
      }

      this.isLoading(true, resourceName);
      const response = await this.probeApiService.updateLockState(probeIds, lockState);

      if (response) {
        return true;
      }

      return false;

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  /**
  * Enable or disable probe editing capabilities
  * Handles validation and API call for probe enable/disable operation
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to enable/disable
  * @param selectedProbes - Array of selected probe objects for validation
  * @param enableState - true to enable, false to disable
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<void> indicating operation success
  */
  public async enableDisableProbes(probeIds: number[], selectedProbes: any[], enableState: boolean, resourceName: string): Promise<boolean> {
    try {

      // Perform validation based on resource type
      if (!this.validateProbeEnableDisablePermissions(probeIds, selectedProbes, enableState, resourceName)) {
        return false;
      }

      if (!this.permissionService.getProbPermission(PermissionAction.EDITABLE_PROBE_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE)
        return false;
      }

      this.isLoading(true, resourceName);
      const response = await lastValueFrom(this.probeApiService.editEnableDisableProbe(probeIds, enableState));
      this.toastrService.success(response.body.message);
      return true;

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  /**
  * Delete probes
  * Handles validation, confirmation dialog, and API call for probe deletion
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to delete
  * @param selectedProbes - Array of selected probe objects for validation
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<void> indicating operation success
  */
  public async deleteProbes(probeIds: number[], selectedProbes: any[], resourceName: string): Promise<boolean> {
    try {
      // Perform validation based on resource type
      if (!this.validateProbePermissions(probeIds, selectedProbes, resourceName)) {
        return false;
      }

      if (!this.permissionService.getProbPermission(PermissionAction.DELETE_PROB_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE)
        return false;
      }

      const basicModelConfig = this.confirmDialogService.getBasicModelConfigForDisableAction(ProbDetailResource);
      const confirmed = await this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText);

      if (!confirmed) {
        return false;
      }

      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.probeApiService.deleteProbes(probeIds));
      this.toastrService.success(response.body.message);
      return true;
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  /**
  * RMA product status for probes
  * Handles validation, confirmation dialog, and API call for probe RMA operation
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to set to RMA
  * @param selectedProbes - Array of selected probe objects for validation
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<void> indicating operation success
  */
  public async rmaProductStatusForProbes(probeIds: number[], selectedProbes: any[], resourceName: string): Promise<boolean> {
    try {
      // Perform validation based on resource type
      if (!this.validateProbeRMAPermissions(probeIds, selectedProbes, resourceName)) {
        return false;
      }

      if (!this.permissionService.getProbPermission(PermissionAction.RMA_PROBE_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE);
        return false;
      }

      const basicModelConfig = this.confirmDialogService.getBasicModelConfigForDisableAction(ProbDetailResource);
      const confirmed = await this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText);

      if (!confirmed) {
        return false;
      }

      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.probeApiService.rmaProductStatusForProbe(probeIds));
      this.toastrService.success(response.body.message);
      return true;
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  /**
  * Disable product status for probes
  * Handles validation, confirmation dialog, and API call for probe disable operation
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to disable
  * @param selectedProbes - Array of selected probe objects for validation
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<void> indicating operation success
  */
  public async disableProductStatusForProbes(probeIds: number[], selectedProbes: any[], resourceName: string): Promise<boolean> {
    try {
      // Perform validation based on resource type
      if (!this.validateProbePermissions(probeIds, selectedProbes, resourceName)) {
        return false;
      }

      if (!this.permissionService.getProbPermission(PermissionAction.DISABLE_PROBE_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE);
        return false;
      }

      const basicModelConfig = this.confirmDialogService.getBasicModelConfigForDisableAction(ProbDetailResource);
      const confirmed = await this.confirmDialogService.confirm(basicModelConfig.title, basicModelConfig.message, basicModelConfig.btnOkText, basicModelConfig.btnCancelText);

      if (!confirmed) {
        return false;
      }

      this.isLoading(true, resourceName);
      const response = await firstValueFrom(this.probeApiService.disableProductStatusForProbe(probeIds));
      this.toastrService.success(response.body.message);
      return true;
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  /**
  * Associate probes with sales order
  * Handles validation and opens customer association popup
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to associate
  * @param selectedProbes - Array of selected probe objects for validation
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<void> indicating operation success
  */
  public async associationProbeWithSalesOrder(probeIds: number[], selectedProbes: any[], resourceName: string): Promise<boolean> {
    try {
      // Perform validation based on resource type
      if (!this.validateProbePermissions(probeIds, selectedProbes, resourceName)) {
        return false;
      }

      if (!this.permissionService.getProbPermission(PermissionAction.ASSOCIATE_CUSTOMER_TO_PROBE_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE);
        return false;
      }

      const salesOrderNumber = resourceName === ProbDetailResource ? selectedProbes[0].salesOrderNumber : null;

      const customerAssociationRequest = new CustomerAssociationModelRequest(resourceName, 'Sales Order Association', 'Submit', 'Cancel', salesOrderNumber);
      const confirmedRequestBody: CustomerAssociationRequest = await this.customerAssociationService.openCustomerAssociationPopup(customerAssociationRequest);

      if (confirmedRequestBody.button) {
        this.isLoading(true, resourceName);
        const response = await firstValueFrom(this.probeApiService.associationProbeWithSalesOrder(probeIds, confirmedRequestBody.basicSalesOrderDetailResponse));
        this.toastrService.success(response.body.message);
        return true;
      }

      return false;
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  /**
  * Update probe features
  * Handles validation and opens feature assignment popup
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to update features for
  * @param selectedProbes - Array of selected probe objects for validation
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<void> indicating operation success
  */
  public async updateProbeFeatures(probeIds: number[], selectedProbes: any[], resourceName: string): Promise<boolean> {
    try {
      // Perform validation based on resource type
      if (!this.validateProbePermissions(probeIds, selectedProbes, resourceName)) {
        return false;
      }

      if (probeIds.length !== 1) {
        this.toastrService.info(Probe_Single_Select_Message);
        return false;
      }

      if (!this.permissionService.getProbPermission(PermissionAction.ASSIGN_FEATURE_TO_PROBE_ACTION)) {
        this.toastrService.error(PERMISSION_ERROR_MESSAGE);
        return false;
      }

      const configureLicenceDetails = new ConfigureLicenceDetails(probeIds[0], selectedProbes[0].type, null);
      const response: ConfigureLicenceResponse = await this.updateFeaturesService.openAssignProbeFeatureModel(this.updateFeaturesService.getAssignProbeBasicModelConfigDetail(), configureLicenceDetails, resourceName);

      if (response.button) {
        this.isLoading(true, resourceName);
        return true;
      }

      return false;
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      this.isLoading(false, resourceName);
      return false;
    }
  }

  // ==================== PROBE VALIDATION METHODS ====================

  /**
  * Validate probe selection and basic permissions
  * <AUTHOR>
  * @param probeIds - Array of probe IDs
  * @param selectedProbes - Array of selected probe objects
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  public validateProbePermissions(probeIds: number[], selectedProbes: any[], resourceName: string): boolean {
    if (resourceName === ProbListResource) {
      // For probe list: validate probe selection (includes empty check)
      return this.validateProbeSelection(probeIds, selectedProbes, resourceName);
    } else if (resourceName === ProbDetailResource) {
      // For probe detail: validate single probe permissions
      return this.validateSingleProbePermissions(selectedProbes[0], resourceName);
    }
    return false;
  }

  /**
  * Validate probe lock/unlock permissions based on resource type
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to lock/unlock
  * @param selectedProbes - Array of selected probe objects
  * @param lockState - true to lock, false to unlock
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns true if validation passes, false otherwise
  */
  public validateProbeLockUnlockPermissions(probeIds: number[], selectedProbes: any[], lockState: boolean, resourceName: string): boolean {
    if (resourceName === ProbListResource) {
      // For probe list: validate probe selection and user country access
      return this.validateProbeSelection(probeIds, selectedProbes, resourceName);
    } else if (resourceName === ProbDetailResource) {
      // For probe detail: validate single probe permissions and state
      return this.validateSingleProbePermissions(selectedProbes[0], resourceName) &&
        this.validateProbeLockState(selectedProbes[0], lockState) &&
        this.moduleValidationService.validateWithUserCountryForSingleRecord(selectedProbes[0]?.country, resourceName, true);
    }
    return false;
  }

  /**
  * Validate probe enable/disable permissions based on resource type
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to enable/disable
  * @param selectedProbes - Array of selected probe objects
  * @param enableState - true to enable, false to disable
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns true if validation passes, false otherwise
  */
  public validateProbeEnableDisablePermissions(probeIds: number[], selectedProbes: any[], enableState: boolean, resourceName: string): boolean {
    if (resourceName === ProbListResource) {
      // For probe list: validate user country access only (as per current implementation)
      if (probeIds.length === 0) {
        this.toastrService.info(Probe_Select_Message);
        return false;
      }
      return this.validateUserCountryAccess(selectedProbes, resourceName);
    } else if (resourceName === ProbDetailResource) {
      // For probe detail: validate single probe permissions and state
      return this.validateProbeEditState(selectedProbes[0], enableState) &&
        this.moduleValidationService.validateWithUserCountryForSingleRecord(selectedProbes[0]?.country, resourceName, true);
    }
    return false;
  }

  /**
  * Validate probe RMA permissions based on resource type
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to set to RMA
  * @param selectedProbes - Array of selected probe objects
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns true if validation passes, false otherwise
  */
  public validateProbeRMAPermissions(probeIds: number[], selectedProbes: any[], resourceName: string): boolean {
    if (resourceName === ProbListResource) {
      // For probe list: validate probe selection (includes empty check and permissions)
      return this.validateProbeSelection(probeIds, selectedProbes, resourceName);
    } else if (resourceName === ProbDetailResource) {
      // For probe detail: validate single probe permissions first
      if (!this.validateSingleProbePermissions(selectedProbes[0], resourceName)) {
        return false;
      }
      // Additional validation for RMA operation on detail page
      return this.validateProbeForRMAOperation(selectedProbes[0]);
    }
    return false;
  }

  /**
  * Validate probe selection for list operations
  * <AUTHOR>
  * @param probeIds - Array of probe IDs
  * @param selectedProbes - Array of selected probe objects
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  private validateProbeSelection(probeIds: number[], selectedProbes: any[], resourceName: string): boolean {
    if (probeIds.length === 0) {
      this.toastrService.info(Probe_Select_Message);
      return false;
    }
    return this.validateUserPermissionsAndCountry(selectedProbes, resourceName);
  }

  /**
  * Validate single probe permissions for detail operations
  * <AUTHOR>
  * @param probe - Single probe object
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  private validateSingleProbePermissions(probe: any, resourceName: string): boolean {
    if (!this.moduleValidationService.validateWithEditStateForSingleRecord(probe?.editable, resourceName)) {
      return false;
    }
    return this.moduleValidationService.validateWithUserCountryForSingleRecord(probe?.country, resourceName, true);
  }

  /**
  * Validate user permissions and country access for selected probes
  * <AUTHOR>
  * @param selectedProbes - Array of selected probe objects
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  private validateUserPermissionsAndCountry(selectedProbes: any[], resourceName: string): boolean {
    const moduleEditState: boolean[] = selectedProbes.map(probe => probe.editable);

    if (!this.moduleValidationService.validateWithEditableWithMultipalRecoard(moduleEditState, resourceName)) {
      return false;
    }

    return this.validateUserCountryAccess(selectedProbes, resourceName);
  }

  /**
  * Validate user country access for selected probes
  * <AUTHOR>
  * @param selectedProbes - Array of selected probe objects
  * @param resourceName - Resource name for validation
  * @returns true if validation passes, false otherwise
  */
  private validateUserCountryAccess(selectedProbes: any[], resourceName: string): boolean {
    const moduleCountry: string[] = this.getProbeAssociatedCountries(selectedProbes);
    return this.moduleValidationService.validateWithUserCountryForMultileRecord(moduleCountry, resourceName, true);
  }

  /**
  * Get probe associated countries from selected probes
  * <AUTHOR>
  * @param selectedProbes - Array of selected probe objects
  * @returns Array of country names
  */
  private getProbeAssociatedCountries(selectedProbes: any[]): string[] {
    return selectedProbes.map(probe => probe.country);
  }

  /**
  * Validate probe lock state for lock/unlock operations
  * <AUTHOR>
  * @param probe - Probe object to validate
  * @param lockState - Desired lock state
  * @returns true if validation passes, false otherwise
  */
  private validateProbeLockState(probe: any, lockState: boolean): boolean {
    if (probe.locked === lockState) {
      const message = lockState ? PROBE_ALREADY_LOCKED : PROBE_ALREADY_UNLOCKED;
      this.toastrService.info(message);
      return false;
    }
    return true;
  }

  /**
  * Validate probe edit state for enable/disable operations
  * <AUTHOR>
  * @param probe - Probe object to validate
  * @param enableState - Desired enable state
  * @returns true if validation passes, false otherwise
  */
  private validateProbeEditState(probe: any, enableState: boolean): boolean {
    if (probe.editable === enableState) {
      const message = enableState ? PROBE_ALREADY_EDIT_ENABLE : PROBE_ALREADY_EDIT_DISABLE;
      this.toastrService.info(message);
      return false;
    }
    return true;
  }

  /**
  * Validate probe for RMA operation
  * <AUTHOR>
  * @param probe - Probe object to validate
  * @returns true if validation passes, false otherwise
  */
  private validateProbeForRMAOperation(probe: any): boolean {
    // Add specific RMA validation logic if needed
    // For now, just validate that probe exists
    return probe != null;
  }

  // ==================== ADDITIONAL PROBE OPERATIONS ====================

  /**
  * Download probes confirmation model
  * Handles probe download operation with validation
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to download
  * @param selectedProbes - Array of selected probe objects for validation
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<boolean> indicating operation success
  */
  public async downloadProbes(probeIds: number[], selectedProbes: any[], resourceName: string): Promise<boolean> {
    try {

      if (resourceName === ProbListResource) {
        // For list resource, use device service download method
        this.deviceService.dowloadSasUriofFeatureLicenseConfirmationModel(resourceName);
      } else if (resourceName === ProbDetailResource) {
        // For detail resource, use probe API service download method
        await this.probeApiService.dowloadSasUriofFeatureLicenseAsync(probeIds, resourceName);
      }

      return true;
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      return false;
    }
  }

  /**
  * Update probe type
  * Handles validation and opens update probe type popup
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to update type for
  * @param selectedProbes - Array of selected probe objects for validation
  * @param currentProbeType - Current probe type (for detail resource)
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<boolean> indicating operation success
  */
  public async updateProbeType(probeIds: number[], selectedProbes: any[], currentProbeType: string | null, resourceName: string): Promise<boolean> {
    try {
      // Perform validation based on resource type
      if (!this.validateProbePermissions(probeIds, selectedProbes, resourceName)) {
        return false;
      }

      const response: boolean = await this.updateProbeTypeService.openUpdateProbeTypePopup(Update_Probe_title, Update_Probe_OkButton, Update_Probe_CancelButton, currentProbeType, probeIds);

      if (response) {
        this.isLoading(true, resourceName);
        return true;
      }

      return false;
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      return false;
    } finally {
      this.isLoading(false, resourceName);
    }
  }

  /**
  * Export probes to CSV
  * Handles CSV export with filter parameters
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to export
  * @param selectedProbes - Array of selected probe objects for validation
  * @param resourceName - Resource name for validation (ProbListResource or ProbDetailResource)
  * @returns Promise<boolean> indicating operation success
  */
  public async exportProbesCSV(probeIds: number[], selectedProbes: any[], resourceName: string): Promise<boolean> {
    try {
      this.isLoading(true, resourceName);

      // Create  filter request body for CSV export since filter is handled by filter component
      const probeFilterRequestBody = this.getProbeSearchRequestBodyForListingApi();
      const probeDownloadCSVRequest = new ProbeDownloadCSVRequest(probeIds, new Date().getTimezoneOffset(), probeFilterRequestBody);

      // Get parameter for device historical data
      const reqParameter = new ProbeDownloadCSVParameterRequest(this.getParameterForDeviceHistoricalData());

      const response = await firstValueFrom(this.probeApiService.generateCSVFileForProbe(probeDownloadCSVRequest, reqParameter));
      const fileName = response.body.fileName;

      // Download the file based on the name received from generate file API call
      const downloadResponse = await firstValueFrom(this.probeApiService.downloadCSVFileForProbe(fileName));
      this.downloadService.downloadExportCSV("List_of_Probe(s).xls", downloadResponse);
      this.toastrService.success('CSV exported successfully');
      return true;
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error);
      return false;
    } finally {
      this.isLoading(false, resourceName);
    }
  }

  /**
  * Transfer probe to another sales order
  * Handles validation and triggers transfer UI for probe detail page
  * <AUTHOR>
  * @param probeIds - Array of probe IDs to transfer
  * @param selectedProbes - Array of selected probe objects for validation
  * @param resourceName - Resource name for validation (ProbDetailResource)
  * @returns Promise<boolean> indicating operation success
  */
  public async transferProbes(probeIds: number[], selectedProbes: any[], resourceName: string): Promise<boolean> {
    try {
      // Perform validation based on resource type
      if (!this.validateProbePermissions(probeIds, selectedProbes, resourceName)) {
        return false;
      }

      if (!this.permissionService.getProbPermission(PermissionAction.TRANSFER_PROBE_ACTION)) {
        return false;
      }

      // For probe detail, we need to trigger the transfer order selection UI
      // This will be handled by the component through a callback or subject
      if (resourceName === ProbDetailResource) {
        // Emit a subject to show transfer order selection
        this.callTransferProbeUISubject(true);
        return true;
      }
      return false;

    } catch (error: any) {
      this.toastrService.error(error.error?.message || 'Transfer probe operation failed');
      return false;
    }
  }

  /**
   * Transfer Probe UI Subject - to communicate with components
   * <AUTHOR>
   */
  private transferProbeUISubject = new Subject<boolean>();

  public getTransferProbeUISubject(): Subject<boolean> {
    return this.transferProbeUISubject;
  }

  public callTransferProbeUISubject(status: boolean): void {
    this.transferProbeUISubject.next(status);
  }

  /**
  * Get parameter for device historical data
  * Helper method to get device historical data parameter
  * <AUTHOR>
  * @returns DeviceHistoricalData parameter
  */
  public getParameterForDeviceHistoricalData(): string {
    // This method should return the appropriate device historical data parameter
    // For now, return a default value
    return this.deviceHistoricalData;
  }

  public setParameterForDeviceHistoricalData(deviceHistoricalData: DeviceHistoricalData): void {
    this.deviceHistoricalData = deviceHistoricalData;
  }


}
