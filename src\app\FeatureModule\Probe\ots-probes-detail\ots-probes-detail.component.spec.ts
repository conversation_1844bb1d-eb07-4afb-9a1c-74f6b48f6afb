import { CommonModule, DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { SessionStorageService } from 'ngx-webstorage';
import { of, Subject, throwError } from 'rxjs';
import { ProbDetailResource, PROBE_DELETE } from '../../../app.constants';
import { Pageable } from '../../../model/common/pageable.model';
import { Sort } from '../../../model/common/sort.model';
import { LicensesRequest } from '../../../model/probe/multiProbe/LicensesRequest.model';
import { ProbeDetailWithConfig } from '../../../model/probe/ProbeDetailWithConfig.model';
import { ProbeHistoryPagableResponse } from '../../../model/probe/ProbeHistoryPagableResponse.model';
import { ProbeHistoryResponse } from '../../../model/probe/ProbeHistoryResponse.model';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { ProbeOperationsEnum } from '../../../shared/enum/Operations/ProbeOperations.enum';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { FeatureHistoryDetailService } from '../../../shared/modalservice/feature-history-detail.service';
import { UpdateFeaturesService } from '../../../shared/modalservice/update-features.service';
import { CommonBooleanValueDisplayPipe } from '../../../shared/pipes/common-boolean-value-display.pipe';
import { BooleanKeyValueMappingDisplayNamePipe } from '../../../shared/pipes/Common/BooleanKeyValueMappingDisplayNamePipe.pipe';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { AssignConfigCheckBoxPipe } from '../../../shared/pipes/Probe/assign-config-checkbox.pipe';
import { AssignConfigDisablePipe } from '../../../shared/pipes/Probe/assign-config-disable.pipe';
import { ConfigBaseResponseDisplayPipe } from '../../../shared/pipes/Probe/config-base-response-display.pipe';
import { FeatureValidityOptionHideShowPipe } from '../../../shared/pipes/Probe/feature-validity-option-hide-show.pipe';
import { FeaturesBaseResponseDisplayPipe } from '../../../shared/pipes/Probe/features-base-response-display.pipe';
import { FeaturesCustomEndDateDisplayPipe } from '../../../shared/pipes/Probe/features-customEndDateDisplay.pipe';
import { FeaturesExpireDateDisplayPipe } from '../../../shared/pipes/Probe/features-expire-datedisplay.pipe';
import { FeaturesRadioButtonPipe } from '../../../shared/pipes/Probe/features-radio-button.pipe';
import { FeaturesStartEndDateDisplay } from '../../../shared/pipes/Probe/features-start-end-dateDisplay.pipe';
import { FeaturesValidityPartNumberDisplayPipe } from '../../../shared/pipes/Probe/features-validity-partNumber-Display.pipe';
import { HidePermissionNamePipe } from '../../../shared/pipes/Role/hidePermissionName.pipe';
import { ProbeApiService } from '../../../shared/Service/ProbeService/probe-api.service';
import { RoleApiCallService } from '../../../shared/Service/RoleService/role-api-call.service';
import { SSOLoginService } from '../../../shared/Service/SSO/ssologin.service';
import { UpdateAssociationService } from '../../../shared/update-association.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { ModuleValidationServiceService } from '../../../shared/util/module-validation-service.service';
import { ValidationService } from '../../../shared/util/validation.service';
import { commonsProviders } from '../../../Tesing-Helper/test-utils';
import { ConfirmDialogService } from '../../CommonComponent/confirmationdialog/confirmation.service';
import { TransferOrderModuleComponent } from '../../TransferOrder/transfer-order-module/transfer-order-module.component';
import { AssignFeaturesComponent } from '../assign-features/assign-features.component';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';
import { OtsProbesDetailComponent } from './ots-probes-detail.component';

describe('OtsProbesDetailComponent', () => {
  let component: OtsProbesDetailComponent;
  let fixture: ComponentFixture<OtsProbesDetailComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let probeApiServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let probeOperationServiceSpy: jasmine.SpyObj<ProbeOperationService>;
  let updateFeaturesServiceSpy: jasmine.SpyObj<UpdateFeaturesService>;
  let featureHistoryDetailServiceSpy: jasmine.SpyObj<FeatureHistoryDetailService>;
  let downloadServiceSpy: jasmine.SpyObj<DownloadService>;
  let commonOperationsServiceSpy: jasmine.SpyObj<CommonOperationsService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let keyValueMappingServiceSpy: jasmine.SpyObj<KeyValueMappingServiceService>;
  let moduleValidationServiceSpy: jasmine.SpyObj<ModuleValidationServiceService>;
  let exceptionServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;

  // Mock data
  const mockProbeDetailWithConfig = new ProbeDetailWithConfig(
    4211,
    "Torso1, USB",
    "ENGDM1.8",
    "PART1234567890",
    "1.0.23",
    1740378882671,
    1740378849244,
    false,
    "system",
    null,
    null,
    null,
    null,
    1740378883139,
    "Algeria",
    false,
    true,
    null,
    false,
    false,
    [new LicensesRequest(1, "Trio 2.0", 1740378849244, -1, false, true)],
    [new LicensesRequest(1, "Gyn", 1740378849244, -1, false, true)],
    ProductStatusEnum.RMA,
    "Transfer Order"
  );

  const mockProbeHistoryResponse = new ProbeHistoryResponse(
    15784,
    1740041739079,
    "akshay.dobariya",
    ["CW Doppler", "Auto EF"],
    ["Heart", "Ob"],
    false
  );

  const mockSort = new Sort(true, false, false);
  const mockPageable = new Pageable(mockSort, 0, 10, 0, true, false);

  const mockProbeHistoryPagableResponse = new ProbeHistoryPagableResponse(
    mockPageable, 5, false, 50, 10, true, mockSort, 10, 0, false, [mockProbeHistoryResponse]
  );

  beforeEach(async () => {
    // Create service spies
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    probeApiServiceSpy = jasmine.createSpyObj('ProbeApiService', ['getProbeDetailInfo', 'getProbeHistory', 'getDeviceListByProbeId']);
    probeOperationServiceSpy = jasmine.createSpyObj('ProbeOperationService', [
      'getProbeDetailLoadingSubject', 'getProbeDetailRefreshSubject', 'getTransferProbeUISubject',
      'changeOperationForProbe', 'transferProbes'
    ]);
    updateFeaturesServiceSpy = jasmine.createSpyObj('UpdateFeaturesService', ['openAssignProbeFeatureModel']);
    featureHistoryDetailServiceSpy = jasmine.createSpyObj('FeatureHistoryDetailService', ['openFeatureHistoryDetailModel']);
    downloadServiceSpy = jasmine.createSpyObj('DownloadService', ['getisLoadingSubjectForProbDetailPage', 'getdownloadZipFileForProbDetailPageSubject']);
    commonOperationsServiceSpy = jasmine.createSpyObj('CommonOperationsService', ['getCommonLoadingSubject', 'accessProbeOperations']);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['accessDataSizes']);
    keyValueMappingServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', [
      'enumOptionToList', 'lockedUnlockOptionList', 'editEnableDisableOptionList'
    ]);
    moduleValidationServiceSpy = jasmine.createSpyObj('ModuleValidationServiceService', [
      'validateWithEditStateForSingleRecord', 'validateWithUserCountryForSingleRecord'
    ]);
    exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    // Setup subject mocks
    const mockLoadingSubject = new Subject<boolean>();
    const mockRefreshSubject = new Subject<any>();
    const mockTransferSubject = new Subject<boolean>();
    const mockDownloadSubject = new Subject<boolean>();
    const mockCommonLoadingSubject = new Subject<boolean>();

    probeOperationServiceSpy.getProbeDetailLoadingSubject.and.returnValue(mockLoadingSubject);
    probeOperationServiceSpy.getProbeDetailRefreshSubject.and.returnValue(mockRefreshSubject);
    probeOperationServiceSpy.getTransferProbeUISubject.and.returnValue(mockTransferSubject);
    downloadServiceSpy.getisLoadingSubjectForProbDetailPage.and.returnValue(mockDownloadSubject);
    downloadServiceSpy.getdownloadZipFileForProbDetailPageSubject.and.returnValue(mockDownloadSubject);
    commonOperationsServiceSpy.getCommonLoadingSubject.and.returnValue(mockCommonLoadingSubject);

    // Setup API response mocks
    probeApiServiceSpy.getProbeDetailInfo.and.returnValue(of(new HttpResponse({ status: 200, body: mockProbeDetailWithConfig })));
    probeApiServiceSpy.getProbeHistory.and.returnValue(of(new HttpResponse({ status: 200, body: mockProbeHistoryPagableResponse })));

    // Setup other service mocks
    commonsServiceSpy.accessDataSizes.and.returnValue(['10', '25', '50']);
    keyValueMappingServiceSpy.enumOptionToList.and.returnValue([]);
    keyValueMappingServiceSpy.lockedUnlockOptionList.and.returnValue([]);
    keyValueMappingServiceSpy.editEnableDisableOptionList.and.returnValue([]);
    commonOperationsServiceSpy.accessProbeOperations.and.returnValue([]);
    featureHistoryDetailServiceSpy.openFeatureHistoryDetailModel.and.returnValue(Promise.resolve(true));

    await TestBed.configureTestingModule({
      declarations: [
        OtsProbesDetailComponent,
        TransferOrderModuleComponent,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        BooleanKeyValueMappingDisplayNamePipe,
        CommonBooleanValueDisplayPipe,
        ConfigBaseResponseDisplayPipe,
        FeaturesStartEndDateDisplay,
        PrintListPipe,
        AssignFeaturesComponent,
        AssignConfigCheckBoxPipe,
        AssignConfigDisablePipe,
        FeaturesBaseResponseDisplayPipe,
        FeatureValidityOptionHideShowPipe,
        FeaturesRadioButtonPipe,
        FeaturesCustomEndDateDisplayPipe,
        FeaturesExpireDateDisplayPipe,
        FeaturesValidityPartNumberDisplayPipe
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [NgbPaginationModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule, CommonModule],
      providers: [
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: ProbeApiService, useValue: probeApiServiceSpy },
        { provide: ProbeOperationService, useValue: probeOperationServiceSpy },
        { provide: UpdateFeaturesService, useValue: updateFeaturesServiceSpy },
        { provide: FeatureHistoryDetailService, useValue: featureHistoryDetailServiceSpy },
        { provide: DownloadService, useValue: downloadServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationsServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingServiceSpy },
        { provide: ModuleValidationServiceService, useValue: moduleValidationServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        UpdateAssociationService,
        CustomerAssociationService,
        ConfirmDialogService,
        ValidationService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        SessionStorageService,
        SSOLoginService,
        RoleApiCallService,
        DatePipe,
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(OtsProbesDetailComponent);
    component = fixture.componentInstance;
    component.probeId = 4211;
    component.resource = ProbDetailResource;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize and call API methods on ngOnInit', () => {
    spyOn<any>(component, 'getProbeDetailInfo').and.callThrough();
    spyOn<any>(component, 'getLicenceHistoryListOfProbeId').and.callThrough();
    component.ngOnInit();

    expect(component['getProbeDetailInfo']).toHaveBeenCalledWith(component.probeId);
    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalledWith(component.probeId);
    expect(component.dataSizes.length).toBeGreaterThan(0);
  });

  it('should clean up all subscriptions on ngOnDestroy', () => {
    component.subscriptionForisloading = new Subject<boolean>().subscribe();
    component.subscriptionForDownloadZipFileProbSubject = new Subject<boolean>().subscribe();
    component.subscriptionForCommonloading = new Subject<boolean>().subscribe();
    component.subscriptionForProbeDetailLoading = new Subject<boolean>().subscribe();
    component.subscriptionForProbeDetailRefresh = new Subject<boolean>().subscribe();
    component.subscriptionForTransferProbeUI = new Subject<boolean>().subscribe();

    const unsubSpy1 = spyOn(component.subscriptionForisloading, 'unsubscribe');
    const unsubSpy2 = spyOn(component.subscriptionForDownloadZipFileProbSubject, 'unsubscribe');
    const unsubSpy3 = spyOn(component.subscriptionForCommonloading, 'unsubscribe');
    const unsubSpy4 = spyOn(component.subscriptionForProbeDetailLoading, 'unsubscribe');
    const unsubSpy5 = spyOn(component.subscriptionForProbeDetailRefresh, 'unsubscribe');
    const unsubSpy6 = spyOn(component.subscriptionForTransferProbeUI, 'unsubscribe');

    component.ngOnDestroy();

    expect(unsubSpy1).toHaveBeenCalled();
    expect(unsubSpy2).toHaveBeenCalled();
    expect(unsubSpy3).toHaveBeenCalled();
    expect(unsubSpy4).toHaveBeenCalled();
    expect(unsubSpy5).toHaveBeenCalled();
    expect(unsubSpy6).toHaveBeenCalled();
  });

  it('should call changeOperationForProbe on changeProbeOperation', () => {
    const mockEvent = { target: { value: ProbeOperationsEnum.DOWNLOAD_PROBES } };
    component.probeDetailWithConfig = mockProbeDetailWithConfig;
    const selectEl = document.createElement('select');
    selectEl.id = 'probeOperation';
    document.body.appendChild(selectEl);
    component.changeProbeOperation(mockEvent);
    expect(probeOperationServiceSpy.changeOperationForProbe).toHaveBeenCalled();
    document.body.removeChild(selectEl);
  });

  it('should emit showProbe when back() is called', () => {
    spyOn(component.showOtsProbe, 'emit');
    component.back();
    expect(component.showOtsProbe.emit).toHaveBeenCalled();
  });

  it('should only call loadFeatureHistoryPage if new history page is selected', () => {
    component.historyPreviousPage = 1;
    component.historyPage = 2;
    spyOn<any>(component, 'getLicenceHistoryListOfProbeId');
    component.loadFeatureHistoryPage(2);
    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalled();
  });

  it('should update history page size on changeFeatureHistoryDataSize', () => {
    const event = { target: { value: 50 } };
    spyOn<any>(component, 'getLicenceHistoryListOfProbeId');
    component.changeFeatureHistoryDataSize(event);
    expect(component.historyItemsPerPage).toBe(50);
    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalled();
  });

  it('should open probe connection history modal', () => {
    component.openProbeConnectionHistory(mockProbeHistoryResponse);
    expect(featureHistoryDetailServiceSpy.openFeatureHistoryDetailModel).toHaveBeenCalled();
  });

  it('should call transferProbes when transferProbe is invoked', async () => {
    component.probeDetailWithConfig = mockProbeDetailWithConfig;
    await component.transferProbe();
    expect(probeOperationServiceSpy.transferProbes).toHaveBeenCalled();
  });

  it('should toggle probe detail and transfer order display correctly', () => {
    spyOn<any>(component, 'getProbeDetailInfo');
    component.transferOrderSelectionToggle(true, false);
    expect(component.transferOrderSelectionDisaplay).toBe(false);
    expect(component.probeDetailDisplay).toBe(true);
    expect(component['getProbeDetailInfo']).toHaveBeenCalledWith(component.probeId);
  });

  it('should refresh all 3 types of data', () => {
    spyOn<any>(component, 'getLicenceHistoryListOfProbeId');
    spyOn<any>(component, 'getProbeDetailInfo');
    component.refreshProbeDetailPage();
    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalled();
    expect(component['getProbeDetailInfo']).toHaveBeenCalled();
  });

  it('should call setLoadingStatus and update loading', () => {
    (component as any).setLoadingStatus(true);
    expect(component.loading).toBeTrue();
  });

  it('should trigger refreshLicenseHistory', () => {
    spyOn<any>(component, 'getLicenceHistoryListOfProbeId');
    component.refreshLicenseHistory();
    expect(component['getLicenceHistoryListOfProbeId']).toHaveBeenCalledWith(component.probeId);
  });

  it('should handle error scenario in getProbeDetailInfo', () => {
    probeApiServiceSpy.getProbeDetailInfo.and.returnValue(
      throwError(() => new HttpErrorResponse({ status: 500 }))
    );
    component.ngOnInit();
    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalled();
  });

  it('should handle non-200 status in getProbeDetailInfo', () => {
    probeApiServiceSpy.getProbeDetailInfo.and.returnValue(of(new HttpResponse<ProbeDetailWithConfig>({ status: 404 })));
    const backSpy = spyOn(component, 'back');
    component.ngOnInit();
    expect(toastrServiceMock.info).toHaveBeenCalledWith(PROBE_DELETE);
    expect(backSpy).toHaveBeenCalled();
  });
});
