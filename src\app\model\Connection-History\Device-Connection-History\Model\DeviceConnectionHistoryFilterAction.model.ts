import { ListingPageReloadSubjectParameter } from "../../../common/listingPageReloadSubjectParameter.model";
import { DeviceConnectionHistorySearchRequestBody } from "./DeviceConnectionHistorySearchRequestBody.model";

export class DeviceConnectionHistoryFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    deviceConnectionHistoryBody: DeviceConnectionHistorySearchRequestBody;

    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $deviceConnectionHistoryBody: DeviceConnectionHistorySearchRequestBody) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.deviceConnectionHistoryBody = $deviceConnectionHistoryBody;
    }

}