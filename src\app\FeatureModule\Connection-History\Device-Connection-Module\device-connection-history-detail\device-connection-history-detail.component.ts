import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-device-connection-history-detail',
  templateUrl: './device-connection-history-detail.component.html',
  styleUrl: './device-connection-history-detail.component.css'
})
export class DeviceConnectionHistoryDetailComponent {

  @Input("deviceConnectionHistoryId") deviceConnectionHistoryId: number;
  @Output("showDeviceConnectionHistoryList") showDeviceConnectionHistoryList = new EventEmitter();
}
